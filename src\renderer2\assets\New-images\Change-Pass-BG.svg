<svg width="400" height="387" viewBox="0 0 400 387" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1997_541)">
<rect width="400" height="387" rx="20" fill="#191A20"/>
<g opacity="0.8" filter="url(#filter0_f_1997_541)">
<circle cx="352" cy="435" r="40" fill="#9786FF"/>
<circle cx="352" cy="435" r="40" fill="url(#paint0_linear_1997_541)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_1997_541_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.04 0.0523858 -0.03749 0.0731898 352 438.249)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="352" cy="435" r="40" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:79.999992370605469,&#34;m01&#34;:-74.979957580566406,&#34;m02&#34;:349.4899902343750,&#34;m10&#34;:104.77156829833984,&#34;m11&#34;:146.37953186035156,&#34;m12&#34;:312.67318725585938},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g filter="url(#filter1_f_1997_541)">
<circle cx="80" cy="-70" r="80" fill="#9786FF"/>
<circle cx="80" cy="-70" r="80" fill="url(#paint2_linear_1997_541)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_1997_541_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.08 0.104772 -0.07498 0.14638 80 -63.5025)"><foreignObject x="-941.285" y="-941.285" width="1882.57" height="1882.57"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="80" cy="-70" r="80" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:159.99998474121094,&#34;m01&#34;:-149.95991516113281,&#34;m02&#34;:74.979965209960938,&#34;m10&#34;:209.54313659667969,&#34;m11&#34;:292.75906372070312,&#34;m12&#34;:-314.65365600585938},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="399" height="386" rx="19.5" stroke="url(#paint4_radial_1997_541)" stroke-opacity="0.4"/>
<defs>
<filter id="filter0_f_1997_541" x="252" y="335" width="200" height="200" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_1997_541"/>
</filter>
<clipPath id="paint1_angular_1997_541_clip_path"><circle cx="352" cy="435" r="40"/></clipPath><filter id="filter1_f_1997_541" x="-100" y="-250" width="360" height="360" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1997_541"/>
</filter>
<clipPath id="paint3_angular_1997_541_clip_path"><circle cx="80" cy="-70" r="80"/></clipPath><linearGradient id="paint0_linear_1997_541" x1="352" y1="419.162" x2="406.3" y2="448.809" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1997_541" x1="80" y1="-101.675" x2="188.6" y2="-42.3818" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_1997_541" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(123.667 -6.525) rotate(113.98) scale(101.703 95.2659)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_1997_541">
<rect width="400" height="387" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
