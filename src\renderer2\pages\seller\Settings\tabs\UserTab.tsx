import React, { useState, useEffect, useRef } from 'react';
import styles from './TabContent.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  FieldErrors,
  FieldValues,
  useForm,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import MultiStateSelector from 'src/renderer2/component/MultiStateSelector/MultiStateSelector';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import ChangePassword from 'src/renderer2/component/changePassword/changePassword';
import { buyerSettingConst, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import {
  formatPhoneNumberWithHyphen,
  unformatPhoneNumber,
} from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';
interface InputFocusState {
  userType: boolean;
  firstName: boolean;
  email: boolean;
  password: boolean;
  phoneNumber: boolean;
}

const UserTab: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
  buyerSettings: any;
  register: any;
  handleSubmit: any;
  errors: any;
  setError: any;
  setValue: any;
  isDirty: any;
  isValid: any;
  watch: any;
  control: any;
  trigger: any;
  resetField: any;
  dirtyFields: any;
  setActiveTab: any;
}> = ({
  register,
  handleSubmit,
  errors,
  setError,
  setValue,
  isDirty,
  isValid,
  watch,
  control,
  trigger,
  resetField,
  dirtyFields,
  setActiveTab,
}) => {
  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    userType: false,
    firstName: false,
    email: false,
    password: false,
    phoneNumber: false,
    searchZipcode: false,
    stateSubscription: false,
  });
  const [openChangePassPopup, setOpenChangePassPopup] = useState(false);
  const { deviceId, isImpersonatedUserLoggedIn, userData }: any =
    useGlobalStore();
  const changePassPopupRef = useRef(null);
  const { mutate: saveUserSettings } = useSaveUserSettings();
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();  

  useEffect(() => {
    setTimeout(() => {
      const firstNameInput = document.getElementById('firstName');
      if (firstNameInput) {
        firstNameInput.focus();
      }
    }, 100)
  }, []);


  // Load user settings from localStorage on component mount
  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleZipValidation = async (): Promise<any> => {
    if(watch('searchZipcode') && watch('searchZipcode').trim() !== ''){
    try {
      const res = await verifyZipCode({ zip_code: watch('searchZipcode') });
      if (res) {
        return true;
      } else {
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    } catch (error) {
        console.log('error', error);
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    }
  };

  const handleInputBlur = async (
    inputName: keyof InputFocusState
  ): Promise<void> => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields[inputName]) {
      if (inputName === 'searchZipcode') {
        const res = await handleZipValidation();
        if (res) {
          setTimeout(() => {
            saveUserSettingsonBlur();
          }, 100);
        }
      } else {
        setTimeout(() => {
          saveUserSettingsonBlur();
        }, 100);
      }
    }
  };

  const userTypes = [
    { title: 'Buyer', value: 'buyer' },
    { title: 'Seller', value: 'seller' },
    { title: 'Admin', value: 'admin' },
  ];

  const changePassPopup = () => {
    if (!isImpersonatedUserLoggedIn) setOpenChangePassPopup(true);
  };

  const saveUserSettingsonBlur = async () => {
    // Create an object with only fields that have values
    const fieldsToValidate = [];
    const userSettingsPayload: any = {};
    if (watch('firstName')) {
      fieldsToValidate.push('firstName');
      userSettingsPayload.user_name = watch('firstName');
    }
    if (watch('email')) {
      if (!errors?.email) {
        fieldsToValidate.push('email');
        userSettingsPayload.email_id = watch('email');
      }
    }
    if (watch('phoneNumber')) {
      if (!errors?.phoneNumber) {
        fieldsToValidate.push('phoneNumber');
        userSettingsPayload.phone = unformatPhoneNumber(watch('phoneNumber'));
      }
    }
    // Only validate fields that have values
    if (fieldsToValidate.length > 0) {
      const isValid = await trigger(fieldsToValidate);
      if (isValid && Object.keys(userSettingsPayload).length > 0) {
        saveUserSettings({
          route: 'user/settings',
          data: userSettingsPayload,
        });

        // Reset dirty state for successfully validated and saved fields
        fieldsToValidate.forEach((fieldName) => {
          const currentValue = watch(fieldName);
          resetField(fieldName, { 
            defaultValue: currentValue,
            keepError: false,
            keepDirty: false,
            keepTouched: true
          });
        });
      }
    }
  };

  return (
    <div className={clsx(styles.tabContent,styles.userTabContent)} ref={changePassPopupRef}>
      <div className={styles.formContainer}>
        {/* USER TYPE */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.userType && styles.focusLbl)}
              htmlFor='userType'
            >
              USER TYPE
            </label>
          </span>
          <span className={styles.col1}>
            <div className={styles.inputCreateAccount}>{userData?.data?.type === "BUYER" ? "Buyer" : userData?.data?.type || 'BUYER'}</div>
          </span>
        </div>

        {/* YOUR FIRST & LAST NAME */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.firstName && styles.focusLbl)}
              htmlFor='firstName'
            >
              YOUR FIRST & LAST NAME
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.firstName && styles.error
                )}
                id='firstName'
                type='text'
                register={register('firstName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('firstName').onBlur(e);
                  handleInputBlur('firstName');
                }}
                onFocus={() => handleInputFocus('firstName')}
                errorInput={errors?.firstName}
                onKeyDown={(e) => {
                  if(e.key === 'Tab'){
                    if(e.shiftKey){
                      setActiveTab('COMPANY');
                    }
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR EMAIL ADDRESS */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.email && styles.focusLbl)}
              htmlFor='email'
            >
              YOUR EMAIL ADDRESS
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.email && styles.error
                )}
                type='email'
                register={register('email')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('email').onBlur(e);
                  handleInputBlur('email');
                }}
                onFocus={() => handleInputFocus('email')}
                errorInput={errors?.email}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR PASSWORD */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.password && styles.focusLbl)}
              htmlFor='password'
            >
              YOUR PASSWORD
            </label>
          </span>
          <span className={styles.col1}>
            <span
              onClick={changePassPopup}
              className={clsx(styles.inputCreateAccount, styles.changePassword)}
              tabIndex={0}
              onKeyDown={(e) => {
                if(e.key === 'Enter'){
                  changePassPopup();
                }
              }}
            >
              Change Password
            </span>
          </span>
        </div>

        {/* YOUR PHONE NUMBER */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.phoneNumber && styles.focusLbl)}
              htmlFor='phoneNumber'
            >
              YOUR PHONE NUMBER
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.phoneNumber && styles.error
                )}
                type='tel'
                register={register('phoneNumber')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('phoneNumber').onBlur(e);
                  handleInputBlur('phoneNumber');
                }}
                onFocus={() => handleInputFocus('phoneNumber')}
                errorInput={errors?.phoneNumber}
                mode='phoneNumberHyphen'
              />
            </InputWrapper>
          </span>
        </div>
      </div>
      <Dialog
        open={openChangePassPopup}
        onClose={(event) => setOpenChangePassPopup(false)}
        transitionDuration={100}
        container={changePassPopupRef.current}
        disableScrollLock={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={(event) => setOpenChangePassPopup(false)}
        >
          <CloseIcon />
        </button>
        <ChangePassword
          closeDialog={() => {
            setOpenChangePassPopup(false);
          }}
          deviceId={deviceId}
        />
      </Dialog>
    </div>
  );
};

export default UserTab;
