import React, { useState, useEffect, useRef } from 'react';
import styles from './TabContent.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  FieldErrors,
  FieldValues,
  useForm,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';
import { userSchema } from '../schemas';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import MultiStateSelector from 'src/renderer2/component/MultiStateSelector/MultiStateSelector';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import ChangePassword from 'src/renderer2/component/changePassword/changePassword';
import { buyerSettingConst, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import {
  formatPhoneNumberWithHyphen,
  unformatPhoneNumber,
} from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';
interface InputFocusState {
  userType: boolean;
  firstName: boolean;
  email: boolean;
  password: boolean;
  phoneNumber: boolean;
  searchZipcode: boolean;
  stateSubscription: boolean;
}

const UserTab: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
  buyerSettings: any;
  register: any;
  handleSubmit: any;
  errors: any;
  setError: any;
  setValue: any;
  isDirty: any;
  isValid: any;
  watch: any;
  control: any;
  trigger: any;
  resetField: any;
  dirtyFields: any;
  setActiveTab: any;
}> = ({
  register,
  handleSubmit,
  errors,
  setError,
  setValue,
  isDirty,
  isValid,
  watch,
  control,
  trigger,
  resetField,
  dirtyFields,
  setActiveTab,
}) => {
  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    userType: false,
    firstName: false,
    email: false,
    password: false,
    phoneNumber: false,
    searchZipcode: false,
    stateSubscription: false,
  });
  const [openChangePassPopup, setOpenChangePassPopup] = useState(false);
  const [states, setStates] = useState<any[]>([]);
  const [selectedStates, setSelectedStates] = useState<number[]>([]);
  const { deviceId, isImpersonatedUserLoggedIn, userData, referenceData }: any =
    useGlobalStore();
  const changePassPopupRef = useRef(null);
  const { mutate: saveUserSettings } = useSaveUserSettings();
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();
  const {buyerSetting , setBuyerSettingInfo} = useBuyerSettingStore();

  useEffect(() => {
    setTimeout(() => {
      const firstNameInput = document.getElementById('firstName');
      if (firstNameInput) {
        firstNameInput.focus();
      }
    }, 100)
  }, []);


  // Load user settings from localStorage on component mount
  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleZipValidation = async (): Promise<any> => {
    if(watch('searchZipcode') && watch('searchZipcode').trim() !== ''){
    try {
      const res = await verifyZipCode({ zip_code: watch('searchZipcode') });
      if (res) {
        return true;
      } else {
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    } catch (error) {
        console.log('error', error);
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    }
  };

  const handleInputBlur = async (
    inputName: keyof InputFocusState
  ): Promise<void> => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields[inputName]) {
      if (inputName === 'searchZipcode') {
        const res = await handleZipValidation();
        if (res) {
          setTimeout(() => {
            saveUserSettingsonBlur();
          }, 100);
        }
      } else {
        setTimeout(() => {
          saveUserSettingsonBlur();
        }, 100);
      }
    }
  };

  const userTypes = [
    { title: 'Buyer', value: 'buyer' },
    { title: 'Seller', value: 'seller' },
    { title: 'Admin', value: 'admin' },
  ];

  const changePassPopup = () => {
    if (!isImpersonatedUserLoggedIn) setOpenChangePassPopup(true);
  };

  // Initialize states from reference data
  useEffect(() => {
    if (referenceData?.ref_states) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  // Sync selectedStates with form data when stateSubscription changes
  useEffect(() => {
    const stateSubscription = watch('stateSubscription');
    if (
      stateSubscription &&
      Array.isArray(stateSubscription) &&
      states.length > 0
    ) {
      // Check if the data contains state codes (strings) or state IDs (numbers)
      if (
        stateSubscription.length > 0 &&
        typeof stateSubscription[0] === 'string'
      ) {
        // Convert state codes to state IDs
        const stateIds = stateSubscription
          .map((stateCode) => {
            const state = states.find((s) => s.code === stateCode);
            return state ? state.id : null;
          })
          .filter((id) => id !== null);
        setSelectedStates(stateIds);
        setValue('stateSubscription', stateIds); // Update form with IDs
      } else {
        // Data is already in ID format
        setSelectedStates(stateSubscription);
      }
    }
  }, [watch('stateSubscription'), states]);

  // Handle state selection changes (no auto-save)
  const handleStateSelectionChange = (newSelectedStates: number[]) => {
    setSelectedStates(newSelectedStates);
    setValue('stateSubscription', newSelectedStates);
  };

  // Handle update states button click (trigger blur to save everything)
  const handleUpdateStates = async (statesToSave: number[]) => {
    // Update the selected states
    setSelectedStates(statesToSave);
    setValue('stateSubscription', statesToSave);

    // Trigger the blur save which will now include the updated state subscription
    await saveUserSettingsonBlur();
  };

  const saveUserSettingsonBlur = async () => {
    // Create an object with only fields that have values
    const fieldsToValidate = [];
    const userSettingsPayload: any = {};

    if (watch('firstName')) {
      fieldsToValidate.push('firstName');
      userSettingsPayload.user_name = watch('firstName');
    }

    if (watch('email')) {
      if (!errors?.email) {
        fieldsToValidate.push('email');
        userSettingsPayload.email_id = watch('email');
      }
    }

    if (watch('phoneNumber')) {
      if (!errors?.phoneNumber) {
        fieldsToValidate.push('phoneNumber');
        userSettingsPayload.phone = unformatPhoneNumber(watch('phoneNumber'));
      }
    }

    if (watch('searchZipcode') && watch('searchZipcode').trim() !== '') {
      if (!errors?.searchZipcode) {
        fieldsToValidate.push('searchZipcode');
        userSettingsPayload.price_search_zip = watch('searchZipcode');
      }
    }

    // Always include state_subscription (even if empty array)
    if (selectedStates !== undefined && selectedStates !== null) {
      // Convert state IDs to state codes
      const stateCodes = selectedStates
        .map((stateId) => {
          const state = states.find((s) => s.id === stateId);
          return state ? state.code : null;
        })
        .filter((code) => code !== null);

      userSettingsPayload.state_subscription = stateCodes;
    }

    // Only validate fields that have values
    if (fieldsToValidate.length > 0) {
      const isValid = await trigger(fieldsToValidate);
      if (isValid && Object.keys(userSettingsPayload).length > 0) {
        saveUserSettings({
          route: 'user/settings',
          data: userSettingsPayload,
        });

        if(fieldsToValidate.includes('searchZipcode')){
          let updatedBuyerSetting = {
            ...buyerSetting,
            price_search_zip: userSettingsPayload.price_search_zip
          };
          setBuyerSettingInfo(updatedBuyerSetting);

        }
        
        // Reset dirty state for successfully validated and saved fields
        fieldsToValidate.forEach((fieldName) => {
          const currentValue = watch(fieldName);
          resetField(fieldName, { 
            defaultValue: currentValue,
            keepError: false,
            keepDirty: false,
            keepTouched: true
          });
        });
      }
    }
  };

  return (
    <div className={clsx(styles.tabContent,styles.userTabContent)} ref={changePassPopupRef}>
      <div className={styles.formContainer}>
        {/* USER TYPE */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.userType && styles.focusLbl)}
              htmlFor='userType'
            >
              USER TYPE
            </label>
          </span>
          <span className={styles.col1}>
            <div className={styles.inputCreateAccount}>{userData?.data?.type === "BUYER" ? "Buyer" : userData?.data?.type || 'BUYER'}</div>
          </span>
        </div>

        {/* YOUR FIRST & LAST NAME */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.firstName && styles.focusLbl)}
              htmlFor='firstName'
            >
              YOUR FIRST & LAST NAME
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.firstName && styles.error
                )}
                id='firstName'
                type='text'
                register={register('firstName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('firstName').onBlur(e);
                  handleInputBlur('firstName');
                }}
                onFocus={() => handleInputFocus('firstName')}
                errorInput={errors?.firstName}
                onKeyDown={(e) => {
                  if(e.key === 'Tab'){
                    if(e.shiftKey){
                      setActiveTab('COMPANY');
                    }
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR EMAIL ADDRESS */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.email && styles.focusLbl)}
              htmlFor='email'
            >
              YOUR EMAIL ADDRESS
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.email && styles.error
                )}
                type='email'
                register={register('email')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('email').onBlur(e);
                  handleInputBlur('email');
                }}
                onFocus={() => handleInputFocus('email')}
                errorInput={errors?.email}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR PASSWORD */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.password && styles.focusLbl)}
              htmlFor='password'
            >
              YOUR PASSWORD
            </label>
          </span>
          <span className={styles.col1}>
            <span
              onClick={changePassPopup}
              className={clsx(styles.inputCreateAccount, styles.changePassword)}
              tabIndex={0}
              onKeyDown={(e) => {
                if(e.key === 'Enter'){
                  changePassPopup();
                }
              }}
            >
              Change Password
            </span>
          </span>
        </div>

        {/* YOUR PHONE NUMBER */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.phoneNumber && styles.focusLbl)}
              htmlFor='phoneNumber'
            >
              YOUR PHONE NUMBER
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.phoneNumber && styles.error
                )}
                type='tel'
                register={register('phoneNumber')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('phoneNumber').onBlur(e);
                  handleInputBlur('phoneNumber');
                }}
                onFocus={() => handleInputFocus('phoneNumber')}
                errorInput={errors?.phoneNumber}
                mode='phoneNumberHyphen'
              />
            </InputWrapper>
          </span>
        </div>

        {/* DEFAULT ZIPCODE FOR PRICE SEARCH */}
        <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.searchZipcode && styles.focusLbl)}
              htmlFor='searchZipcode'
            >
              DEFAULT ZIPCODE FOR <br /> PRICE SEARCH
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.searchZipcode && styles.error
                )}
                type='text'
                register={register('searchZipcode')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('searchZipcode').onBlur(e);
                  handleInputBlur('searchZipcode');
                }}
                onFocus={() => handleInputFocus('searchZipcode')}
                errorInput={errors?.searchZipcode}
                maxLength={5}
                mode='wholeNumber'
                onKeyDown={(e) => {
                  if(e.key === 'Tab' && !e.shiftKey){
                      setActiveTab('SHIPMENTS');
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>

        {/* STATE SELECTION FOR PRICING */}
        <div
          className={clsx(
            styles.formGroupInput,
            styles.stateSelectionGroup,
            styles.bdrBtm0
          )}
        >
          <span className={styles.col1}>
            <label
              className={clsx(
                isInputFocused.stateSubscription && styles.focusLbl
              )}
              htmlFor='stateSubscription'
            >
              STATE SELECTION FOR PRICING
            </label>
          </span>
          <span className={styles.col1}>
            <MultiStateSelector
              states={states}
              selectedStates={selectedStates}
              onSelectionChange={handleStateSelectionChange}
              onUpdateStates={handleUpdateStates}
              onFocus={() => handleInputFocus('stateSubscription')}
              onBlur={() => handleInputBlur('stateSubscription')}
              error={errors?.stateSubscription}
            />
          </span>
        </div>
      </div>
      <Dialog
        open={openChangePassPopup}
        onClose={(event) => setOpenChangePassPopup(false)}
        transitionDuration={100}
        container={changePassPopupRef.current}
        disableScrollLock={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={(event) => setOpenChangePassPopup(false)}
        >
          <CloseIcon />
        </button>
        <ChangePassword
          closeDialog={() => {
            setOpenChangePassPopup(false);
          }}
          deviceId={deviceId}
        />
      </Dialog>
    </div>
  );
};

export default UserTab;
