import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { watch } from 'fs';
import { Autocomplete, ClickAwayListener, Dialog, Select, TextField } from '@mui/material';
import { Watch } from '@mui/icons-material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import CustomAddressComponent from '../components/CustomAddressComponent';
import axios from 'axios';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { useImmer } from 'use-immer';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';

interface InputFocusState {
    parentCompanyName: boolean;
    companyDBAName: boolean;
    companyType: boolean;
    companyAddress: boolean;
    billingContactName: boolean;
    billingContactEmail: boolean;
    sendInvoicesTo: boolean;
    sellerAddress: boolean;
    sendOrderDocsTo: boolean;
}

const CompanyTab: React.FC<{ register: any, handleSubmit: any, control: any, watch: any, setValue: any, getValues: any, setError: any, clearErrors: any, errors: any, trigger: any , isDirty: any, yourCompanyList: any, resetField: any, dirtyFields: any , setActiveTab: any}> = ({ register, handleSubmit, control, watch, setValue, getValues, setError, clearErrors, errors, trigger , isDirty, yourCompanyList, resetField, dirtyFields, setActiveTab }) => {
    const selectCompanyHQAddressRef = useRef(null);
    const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
        parentCompanyName: false,
        companyDBAName: false,
        companyType: false,
        companyAddress: false,
        billingContactName: false,
        billingContactEmail: false,
        sendInvoicesTo: false,
        sellerAddress: false,
        sendOrderDocsTo: false,
    });
    const [customAddressComponentOpen, setCustomAddressComponentOpen] = useState(false);
    const [focusedInput, setFocusedInput] = useState<string | null>(null);
    const [validationInProgress, setValidationInProgress] = useState(true);
    const {referenceData}: any = useGlobalStore();
    const [states, setStates] = useState<any[]>([]);
    const [yourCompanyValue, setYourCompanyValue] = useImmer(null);
    const [yourCompanyInput, setYourCompanyInput] = useState("");
    const { mutate: saveUserSettings } = useSaveUserSettings();

    useEffect(() => {   
        setTimeout(() => {
            const parentCompanyNameInput = document.getElementById('parentCompanyName');
            if (parentCompanyNameInput) {
                parentCompanyNameInput.focus();
            }
        }, 100)
    }, []);

    useEffect(() => {
        if(referenceData?.ref_states) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        if(watch('companyDBAName') !== "") {
            setYourCompanyInput(watch('companyDBAName') ?? "")
        }
    }, [watch('companyDBAName')])


    const handleInputFocus = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: true,
        }));
    };

    const handleInputBlur = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: false,
        }));
        if(dirtyFields[inputName]){
            if(focusedInput !== "sellerAddress" && focusedInput !== "companyAddress") {
                setTimeout(() => {
                    handleSaveCompanySettings();
                }, 100)
            }
        }
    };

    const companyTypes = [
        { title: 'Fabricator', value: 'Fabricator' },
        { title: 'Constructor', value: 'Constructor' },
        { title: 'Distributor', value: 'Distributor' },
        { title: 'OEM', value: 'OEM' },
    ];

    const handleLocationAddressClick = () => {
        setFocusedInput('sellerAddress');
        setCustomAddressComponentOpen(true);

    }

    const handleLocationAddressFocus = () => {
        setFocusedInput('sellerAddress');
        setCustomAddressComponentOpen(true);
    }

    const handleLocationAddressBlur = () => {
        setCustomAddressComponentOpen(false);
        setFocusedInput(null);
    }

    const handleCustomAddressComponentClose =async () => {
        setCustomAddressComponentOpen(false);
        if( focusedInput && dirtyFields[focusedInput]){
            if(focusedInput === 'sellerAddress' || focusedInput === 'companyAddress') {
                // Only validate if both zip and state have values
                const zipValue = watch(`${focusedInput}.zip`);
            const stateValue = watch(`${focusedInput}.state`);
            if (zipValue && stateValue) {
                const isZipCodeVaild = await handleStateZipValidation(`${focusedInput}.zip`, `${focusedInput}.state`)
                if(isZipCodeVaild) {
                    setTimeout(() => {
                        handleSaveCompanySettings();
                    }, 100)
                }
            }else{
                setTimeout(() => {
                    handleSaveCompanySettings();
                }, 100)
                }
            }
        }
        setFocusedInput(null);
    }

    const handleCompanyHQAddressClick = () => {
        setFocusedInput('companyAddress');
        setCustomAddressComponentOpen(true);
    }
 
    const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
        try {
            if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
                setValidationInProgress(false)
                const payload = {
                data: {
                    state_id: getValues(stateCode),
                    zip_code: parseInt(getValues(zipCode)),
                },
            };
            const checkStateZipResponse = await axios.post(
                import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
                payload
            );
            if (checkStateZipResponse.data.data === true) {
                clearErrors([stateCode, zipCode]);
                return true;
            } else {
                setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
                setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
                return false;
            }
            setValidationInProgress(true)
        }
     } catch (error) {
            console.error(error)
        }
    };

    useEffect(() => {
        const stateField = `${focusedInput}.state`;
        const zipField = `${focusedInput}.zip`;
        handleStateZipValidation(zipField, stateField);
    }, [watch('sellerAddress.zip'), watch('sellerAddress.state'), watch('companyAddress.zip'), watch('companyAddress.state')])

    const handleSaveCompanySettings = async () => {
        try{
            // Create an object with only fields that have values
            const fieldsToValidate = [];
            const userSettingsPayload: any = {};

            if (watch('parentCompanyName') && watch('parentCompanyName') !== "") {
                fieldsToValidate.push('parentCompanyName');
                userSettingsPayload.company_name = watch('parentCompanyName');
            }
            if (watch('companyDBAName') && watch('companyDBAName') !== "") {
                fieldsToValidate.push('companyDBAName');
                userSettingsPayload.client_company = watch('companyDBAName');
            }
            if (watch('sendInvoicesTo') && watch('sendInvoicesTo') !== "") {
                const isValid = await trigger('sendInvoicesTo');
                if(isValid) {
                    userSettingsPayload.send_invoices_to = watch('sendInvoicesTo');
                }
            }
            if (watch('sendOrderDocsTo') && watch('sendOrderDocsTo') !== "") {
                const isValid = await trigger('sendOrderDocsTo');
                if(isValid) {
                    userSettingsPayload.shipping_docs_to = watch('sendOrderDocsTo');
                }
            }
            if (watch('billingContactName') && watch('billingContactName') !== "") {
                fieldsToValidate.push('billingContactName');
                userSettingsPayload.billing_contact_name = watch('billingContactName');
            }
            if (watch('billingContactEmail') && watch('billingContactEmail') !== "") {
                const isValid = await trigger('billingContactEmail');
                if(isValid) {
                    userSettingsPayload.billing_email_id = watch('billingContactEmail');
                }
            }
            // Check if any sellerAddress fields are filled
            if (
                watch('sellerAddress.line1') && watch('sellerAddress.line1') !== "" ||
                watch('sellerAddress.city') && watch('sellerAddress.city') !== "" ||
                watch('sellerAddress.state') && watch('sellerAddress.state') !== "" ||
                watch('sellerAddress.zip') && watch('sellerAddress.zip') !== ""
            ) {
                // Check if there are existing errors for sellerAddress
                    const hasExistingErrors = errors?.sellerAddress?.line1 ||
                        errors?.sellerAddress?.line2 ||
                    errors?.sellerAddress?.city ||
                    errors?.sellerAddress?.state ||
                    errors?.sellerAddress?.stateCode ||
                    errors?.sellerAddress?.zip;

                // Only add to validation if no existing errors (to preserve manual errors)
                if (!hasExistingErrors) {
                    fieldsToValidate.push('sellerAddress');
                    // Add all fields to the payload
                    userSettingsPayload.address = {
                        line1: watch('sellerAddress.line1'),
                        line2: watch('sellerAddress.line2') || null,
                        city: watch('sellerAddress.city'),
                        state_id: watch('sellerAddress.state'),
                        zip: watch('sellerAddress.zip')
                    }
                }

            }
            if (
                watch('companyAddress.line1') && watch('companyAddress.line1') !== "" ||
                watch('companyAddress.city') && watch('companyAddress.city') !== "" ||
                watch('companyAddress.state') && watch('companyAddress.state') !== "" ||
                watch('companyAddress.zip') && watch('companyAddress.zip') !== ""
            ) {
                // Check if there are existing errors for sellerAddress
                const hasExistingErrors = errors?.companyAddress?.line1 ||
                    errors?.companyAddress?.line2 ||
                    errors?.companyAddress?.city ||
                    errors?.companyAddress?.state ||
                    errors?.companyAddress?.stateCode ||
                    errors?.companyAddress?.zip;

                // Only add to validation if no existing errors (to preserve manual errors)
                if (!hasExistingErrors) {
                    fieldsToValidate.push('companyAddress');
                    // Add all fields to the payload
                    userSettingsPayload.company_address = {
                        line1: watch('companyAddress.line1'),
                        line2: watch('companyAddress.line2') || null,
                        city: watch('companyAddress.city'),
                        state_id: watch('companyAddress.state'),
                        zip: watch('companyAddress.zip')
                    }
                }
            }
            // Only validate fields that have values
            if (fieldsToValidate.length > 0) {
                const isValid = await trigger(fieldsToValidate);
                if (isValid && Object.keys(userSettingsPayload).length > 0) {
                    saveUserSettings({route: 'user/settings/company', data: userSettingsPayload})
                    // Reset dirty state for successfully validated and saved fields
                    fieldsToValidate.forEach((fieldName) => {
                        const currentValue = watch(fieldName);
                        resetField(fieldName, {
                            defaultValue: currentValue,
                            keepError: false,
                            keepDirty: false,
                            keepTouched: true
                        });
                    });
                }
            }
    }catch(err){
        console.error(err)
    }
    }

    const handleCompanyHQAddressKeyDown = (e: React.KeyboardEvent<HTMLSpanElement>) => {
        if(e.key === 'Enter'){
            handleCompanyHQAddressClick();
        }
    }

    const handleLocationAddressKeyDown = (e: React.KeyboardEvent<HTMLSpanElement>) => {
        if(e.key === 'Enter'){
            handleLocationAddressClick();
        }
    }

    return (
        <div className={styles.tabContent} ref={selectCompanyHQAddressRef}>
            <div className={styles.formContainer}>
                {/* PARENT COMPANY NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.parentCompanyName && styles.focusLbl)} htmlFor="parentCompanyName">
                            PARENT COMPANY NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount)}
                        id='parentCompanyName'
                        tabIndex={0} 
                        onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: false,
                            }));
                        }}
                        >{watch('parentCompanyName')}</span>
                    </span>
                </div>

                {/* COMPANY D.B.A. NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyDBAName && styles.focusLbl)} htmlFor="companyDBAName">
                            COMPANY D.B.A. NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                    <span className={clsx(styles.autocompleteContainer,'autocompleteContainer', errors.companyDBAName && styles.borderOfError)}>
                        
                                <Controller
                                    name="companyDBAName"
                                    control={control}
                                    render={({ field: { ...rest } }) => (
                                        <Autocomplete
                                            freeSolo
                                            value={yourCompanyValue}
                                            onChange={(event, value) => {
                                                setYourCompanyValue(value);
                                                rest.onChange(value ?? null);
                                            }}
                                            onFocus={() => {
                                                setIsInputFocused((prevState) => ({
                                                    ...prevState,
                                                    companyDBAName: true,
                                                }));
                                            }}
                                            inputValue={yourCompanyInput}
                                            className={'companySelectDropdown'}
                                            onInputChange={(event, newInputValue) => {
                                                setYourCompanyInput(newInputValue);
                                                rest.onChange(newInputValue)
                                            }}
                                            onBlur={() => {
                                                setIsInputFocused((prevState) => ({
                                                    ...prevState,
                                                    companyDBAName: false,
                                                }));
                                                handleInputBlur('companyDBAName')
                                            }}
                                            classes={{
                                                paper: clsx(styles.autocompleteDropdown,styles.autocompleteDBA),
                                                listbox: styles.autocompleteListbox,
                                                option: styles.autocompleteOption,
                                                }}
                                            id="controllable-states-demo"
                                            disablePortal={true}
                                            options={yourCompanyList?.length ? yourCompanyList : []}
                                            sx={{ width: '100%' }}
                                            renderInput={(params) => (
                                            <TextField className={styles.companyInput} {...params}  />
                                            )}
                                            getOptionLabel={(item) => {
                                                return item ?? "";
                                            }}
                                        />
                                    )}
                                />
                            </span>
                    </span>
                </div>

                {/* COMPANY HQ ADDRESS */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyAddress && styles.focusLbl)} htmlFor="companyAddress">
                            COMPANY HQ ADDRESS
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span
                            onFocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyAddress: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyAddress: false,
                                }));
                            }}
                            tabIndex={0} onClick={handleCompanyHQAddressClick} className={clsx(styles.inputCreateAccount, (errors?.companyAddress) && styles.error)} onKeyDown={handleCompanyHQAddressKeyDown} >
                            <span className={styles.locationAdressPreviewContainer}>
                                {[
                                    watch("companyAddress.line1"),
                                    watch("companyAddress.line2"),
                                    watch("companyAddress.city"),
                                    watch("companyAddress.stateCode"),
                                    watch("companyAddress.zip")
                                ].filter(Boolean).join(", ")}
                            </span>
                        </span>
                    </span>
                </div>

                {/* YOUR LOCATION ADDRESS */}
                <div className={styles.formGroupInput} id="locationAddressDiv">
                    <span className={styles.col1}>
                        <label className={clsx((isInputFocused.sellerAddress) && styles.focusLbl)} htmlFor="sellerAddress">   
                            YOUR LOCATION ADDRESS
                        </label>
                    </span>
                    <span className={styles.col1}>
                        {
                            <span 
                            onFocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    sellerAddress: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    sellerAddress: false,
                                }));
                            }}
                            tabIndex={0} onClick={handleLocationAddressClick} onKeyDown={handleLocationAddressKeyDown} className={clsx(styles.inputCreateAccount, (errors.sellerAddress) && styles.error)}>
                             <span className={styles.locationAdressPreviewContainer}>
                                {[
                                    watch("sellerAddress.line1"),
                                    watch("sellerAddress.line2"),
                                    watch("sellerAddress.city"),
                                    watch("sellerAddress.stateCode"),
                                    watch("sellerAddress.zip")
                                ].filter(Boolean).join(", ")}
                                </span>
                            </span>
                        }

                    </span>
                </div>

                {/* BILLING CONTACT NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactName && styles.focusLbl)} htmlFor="billingContactName">
                            BILLING CONTACT NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactName && styles.error)}
                                type='text'
                                register={register("billingContactName")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactName").onBlur(e);
                                    handleInputBlur('billingContactName')
                                }}
                                onFocus={() => handleInputFocus('billingContactName')}
                                errorInput={errors?.billingContactName}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* BILLING CONTACT EMAIL */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactEmail && styles.focusLbl)} htmlFor="billingContactEmail">
                            BILLING CONTACT EMAIL
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactEmail && styles.error)}
                                type='email'
                                register={register("billingContactEmail")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactEmail").onBlur(e);
                                    handleInputBlur('billingContactEmail')
                                }}
                                onFocus={() => handleInputFocus('billingContactEmail')}
                                errorInput={errors?.billingContactEmail}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* SEND INVOICES TO */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendInvoicesTo && styles.focusLbl)} htmlFor="sendInvoicesTo">
                            SEND INVOICES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                    <span className={clsx(styles.inputCreateAccount , styles.arBryzosCom)} tabIndex={0} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: false,
                            }));
                        }}
                        >
                            {watch('sendInvoicesTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>

                {/* SEND REMITTANCES TO */}
                <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendOrderDocsTo && styles.focusLbl)} htmlFor="sendOrderDocsTo">
                        SEND ORDER DOCS TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount , styles.arBryzosCom)} tabIndex={0} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: false,
                            }));
                        }}
                        onKeyDown={(e) => {
                            if(e.key === 'Tab' && !e.shiftKey){
                                setActiveTab('USER');
                            }
                        }}
                        >
                            {watch('sendOrderDocsTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>
            </div>
            
            <Dialog
                open={customAddressComponentOpen && focusedInput}
                onClose={(event) => handleCustomAddressComponentClose()}
                transitionDuration={100}
                disableScrollLock={true}
                container={selectCompanyHQAddressRef.current}
                
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid transparent',
                    borderRadius: '0px 0px 20px 20px',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
            >
                <button className={styles.closeIcon} onClick={(event) => handleCustomAddressComponentClose()}><CloseIcon /></button>
                <CustomAddressComponent
                    focusedInput={focusedInput}
                    States={states}
                    register={register}
                    handleInputBlur={handleInputBlur}
                    handleInputFocus={handleInputFocus}
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    setCustomAddressComponentOpen={setCustomAddressComponentOpen}
                    setFocusedInput={setFocusedInput}
                />
            </Dialog>
        </div>
    );
};

export default CompanyTab; 
