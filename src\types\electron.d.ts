/**
 * This declaration file adds type definitions for the Electron object exposed
 * to window by the preload script
 */

interface ElectronAPI {
  send: (options: { channel: string; data?: any }) => void;
  receive: (channel: string, func: (...args: any[]) => void) => void;
  invoke: (channel: string, data?: any) => Promise<any>;
  sendSync: (options: { channel: string; data?: any }) => any;
  handleZoom: (callback?: () => void) => void;
  isWeb: boolean;
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}

export {}; 