// @ts-nocheck
import clsx from 'clsx';
import { CustomMenu } from '../buyer/CustomMenu';
import styles from './onboardingDetails.module.scss'
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { schema } from './onboardingDetailsSchema';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { EmptyString, routes, userTypes } from '../../common';
import { ReactComponent as ErrorEmailIcon } from '../../assets/images/errorEmail.svg';
import { ReactComponent as ShowPassIcon } from '../../assets/images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../assets/images/hide-pass.svg';
import { ReactComponent as ChooseOneIcon } from '../../assets/New-images/Choose-One.svg';
import { ReactComponent as BackArrow } from '../../assets/New-images/Create-Account/arrow-left.svg';
import OnboardingFooter from './onboardingFooter';
import { Autocomplete, Dialog, Fade, Tooltip } from '@mui/material';
import { getChannelWindow, useGetDeviceIdApproval, useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import { Auth } from 'aws-amplify';
import SearchHeader from '../SearchHeader';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import CustomPasswordField from 'src/renderer2/component/CustomPasswordField';


function OnboardingDetails() {
  const { setShowLoader, setExistingUserEmail } = useGlobalStore();
  const navigate = useNavigate();
  const location = useLocation();
  const userData = location.state;
  const channelWindow = getChannelWindow();
  const formContainerRef = useRef(null);
  const { register, handleSubmit, getValues, clearErrors, setValue, watch, setError, control, formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
    resolver: yupResolver(schema),
    mode: "onBlur"
  });

  const showCategory = true;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isCompanyDropdownOpen, setIsCompanyDropdownOpen] = useState(false)
  const [isInputFocused, setIsInputFocused] = useState({
    userType: false,
    CompanyName: false,
    CompanyEntity: false,
    ZipCode: false,
    FirstName: false,
    LastName: false,
    EmailAddress: false,
    ReEnterEmailAddress: false,
    submitBtn: false,
  });
  const [companyNameInput, setCompanyNameInput] = useState("");
  const [companyNameValue, setCompanyNameValue] = useState(null);
  const [openCompanyName, setOpenCompanyName] = useState(false);
  const [onboardCompanies, setOnboardCompanies] = useState([]);
  const [companyEntityInput, setCompanyEntityInput] = useState("");
  const [companyEntityValue, setCompanyEntityValue] = useState(null);
  const [onboardCompanyEntities, setOnboardCompanyEntities] = useState([]);
  const [disableCompanyEntity, setDisableCompanyEntity] = useState(true);
  const [isSelectedCompanyName, setIsSelectedCompanyName] = useState(false);
  const [companyNameSuggestedText, setCompanyNameSuggestedText] = useState("");
  const [isTrialDialogOpen, setIsTrialDialogOpen] = useState(false);

  const [isFocused, setIsFocused] = useState(false);

 
  const [passwordVisibility, setPasswordVisibility] = useState({
    password1: true,
    password2: true,
  });

  const checkDeviceIdApproval = useGetDeviceIdApproval();

  const togglePasswordVisibility = (field) => {
      setPasswordVisibility((prevState) => ({
          ...prevState,
          [field]: !prevState[field],
      }));
  };

  const handleTrialDialogOpen = () => {
    setIsTrialDialogOpen(true);
  };

  const handleTrialDialogClose = () => {
    setIsTrialDialogOpen(false);
  };

  const handleTrialDialogConfirm = () => {
    setIsTrialDialogOpen(false);
  };

  useEffect(() => {
    if(channelWindow?.windowStore){
      const isOnboardingFlowDisabled = window.electron.sendSync({ channel: channelWindow.windowStore, data: 'check'});
      if(!isOnboardingFlowDisabled) handleTrialDialogOpen();
    }
    axios.get(import.meta.env.VITE_API_SERVICE + '/user/company')
      .then((res) => {
        if (res?.data?.data && typeof res.data.data !== 'string' ) {
          setOnboardCompanies(res.data.data);
        }
      })
      .catch((error) => {
        console.error(error)
      })
  }, [])

  useEffect(() => {
    if (userData?.data) {
      setValue("userType", userData?.data.userType)
      setCompanyNameInput(userData?.data.companyName)
      setValue("companyName", userData?.data.companyName)
      setValue("firstName", userData?.data.firstName)
      setValue("lastName", userData?.data.lastName)
      setValue("emailAddress", userData?.data.emailAddress)
      setValue("reEnterEmailAddress", userData?.data.emailAddress)
      if (userData?.errorMessage) {
        setValue("reEnterEmailAddress", "")
        setError("emailAddress", { message: userData.errorMessage }, { shouldFocus: true })
      }
      setValue("zipCode", userData?.data.zipCode)
      setValue("companyEntity", userData?.data.companyEntity)
      userData.zipcodeErrorMessage && setError("zipCode", { message: 'Zip is not valid' })
    }
  }, [userData])

  useEffect(() => {
    if(watch('companyName')?.length > 1){
      setDisableCompanyEntity(false)
      const companyData = onboardCompanies?.find((companyData) => companyData.company_name === watch("companyName"))
      setOnboardCompanyEntities(companyData?.client_company ?? [])
    }else{
      setDisableCompanyEntity(true)
      setOnboardCompanyEntities([])
    }
  },[watch('companyName')])

  useEffect(() => {
    if (companyNameValue) {
      const top = document.querySelector(`[company_id="${companyNameValue.id}"]`)?.offsetTop;
      document.getElementById("combo-box-demo-listbox") && (document.getElementById("combo-box-demo-listbox").scrollTop = top);
      setIsSelectedCompanyName(false)
    }
  }, [isSelectedCompanyName])

  const handleInputFocus = (inputName) => {
    setIsInputFocused((inputState) => ({
      ...inputState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName) => {
    setIsInputFocused((inputState) => ({
      ...inputState,
      [inputName]: false,
    }));
  };
  const isAnyInputFocused = Object.values(isInputFocused).some((state) => state);

  const onSubmit = async (data) => {
    try {
      setShowLoader(true)
      const verifyOnBoardUserEmailPayload = {
        data: {
          email_id: data.emailAddress,
        },
      };
      const verifyOnBoardUserZipcodePayload = {
        data: {
          zip_code: data.zipCode,
        },
      };
      const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/verifyOnBoardUserEmail", verifyOnBoardUserEmailPayload);
      const zipcodeResponseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/verifyZipCode", verifyOnBoardUserZipcodePayload)
      if (responseData.data.data?.error_message || zipcodeResponseData.data.data?.error_message) {
        navigate(routes.onboardingDetails, {
          state: {
            data,
            errorMessage: responseData.data.data.error_message,
            zipcodeErrorMessage: zipcodeResponseData.data.data.error_message,
            tncCheckbox: false,
          },
        });
        setShowLoader(false)
      } else {
        // if (userData?.data?.userType && userData?.tncCheckbox && userData?.tncData) {
        //   const payload = {
        //     data: {
        //       user_type: data.userType,
        //       company_name: data.companyName,
        //       first_name: data.firstName,
        //       last_name: data.lastName,
        //       email_id: data.emailAddress,
        //       bryzos_terms_condtion_id: userData?.tncData.id,
        //       accepted_terms_and_condition:
        //         userData?.tncData.terms_conditions_version,
        //       zip_code: data.zipCode,
        //       client_company: data.companyEntity
        //     },
        //   };
        //   const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/onBoard", payload);
        //   if (responseData.data.data.error_message) {
        //     navigate(routes.onboardingDetails, {
        //       state: {
        //         data,
        //         errorMessage: responseData.data.data.error_message,
        //         tncCheckbox: userData?.tncCheckbox,
        //       },
        //     });
        //     setShowLoader(false)
        //   } else {
        //     navigate(routes.onboardingThankYou);
        //   }
        // } 
        // else {
          navigate(routes.onboardingTnc, { state: {data, isTncChecked: userData?.tncCheckbox} });
        // }

      }


    } catch (error) {
      console.error(error);
    }
  };

  const handleEmailBlur = (e, from) => {
    const emailAddress = getValues('emailAddress')?.trim();
    const reEnterEmailAddress = getValues('reEnterEmailAddress')?.trim();
    const emailPattern = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if(emailPattern.test(emailAddress) && e.target.name === 'emailAddress'){
      handleDeviceIdApproval(emailAddress);
    }

    if (emailPattern.test(emailAddress) && emailPattern.test(reEnterEmailAddress) && emailAddress === reEnterEmailAddress) {
      clearErrors(["emailAddress", "reEnterEmailAddress"]);
    } else {
      if (!emailPattern.test(emailAddress)) {
        setError("emailAddress", { message: "Email is not valid" });
      } else {
        setError("emailAddress", { message: "Email does not match!" });
      }
      if (!emailPattern.test(reEnterEmailAddress) && reEnterEmailAddress.length !== 0) {
        setError("reEnterEmailAddress", { message: "Email is not valid" });
      } else {
        setError("reEnterEmailAddress", { message: "Email does not match!" });
      }
    }
    if(from === 'emailAddress' && reEnterEmailAddress.length === 0){
      clearErrors("reEnterEmailAddress");
    }
  }

  const handlePasswordBlur = () => {
    const password = getValues('password')?.trim();
    const confirmPassword = getValues('confirmPassword')?.trim();
    if(password?.length && confirmPassword?.length){
        if (password === confirmPassword) {
          clearErrors(["password", "confirmPassword"]);
        } else{ 
            setError("password", { message: "Password does not match!" });
        }
    }
  }

  const handleDeviceIdApproval = async (email) => {
    try {
      const res = await checkDeviceIdApproval.mutateAsync(email);
      if(res?.data?.data?.status){
        setExistingUserEmail(email);
        navigate(routes.loginPage)
      }
    } catch (error) {
      console.error(error)
    }
  }


  return (
    <div className={styles.createAccountMain}>
      {/* <div className={styles.onboardingLogo}>
        <img src='/onboardingLogo.png' />
      </div> */}
      <div className={styles.createAccountFormContainer}>
        <span className={styles.joinBryzosText}>JOIN BRYZOS</span>
        <div className={styles.createAccountForm} ref={formContainerRef}>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.userType && styles.focusLbl)} >Choose User Type</label>
            </span>
            <span className={styles.col1}>
              <div className={styles.radioGroupContainer}>
                {/* <span className={styles.chosseOneIcon}><ChooseOneIcon /></span> */}
                <label
                  tabIndex={0}
                  className={clsx(`${styles.radioButtonLeft} ${(watch('userType') === "BUYER") ? clsx(styles.selected, styles.buyerSelected) : ""}`, styles.radioButton)}
                  onClick={() => {
                    setValue('userType', 'BUYER')
                  }}
                  onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                      setValue('userType', 'BUYER');
                    }
                  }}
                  onFocus={() => {
                    handleInputFocus('userType')
                  }}
                  onBlur={() => {
                    handleInputBlur('userType')
                  }}
                >
                  <input
                    type="radio"
                    {...register("userType")}
                    value="BUYER"
                    className={styles.hiddenRadio}
                    onChange={(e) => {
                      register('userType').onChange(e)
                    }}
                  />
                  BUYER
                </label>
                <label
                  tabIndex={1}
                  className={clsx(`${styles.radioButtonRight} ${(watch('userType') === "SELLER") ? clsx(styles.selected, styles.sellerSelected) : ""}`, styles.radioButton)}
                  onClick={() => {
                    setValue('userType', 'SELLER')
                  }}
                  onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                      setValue('userType', 'SELLER');
                    }
                  }}
                  onFocus={() => {
                    handleInputFocus('userType')
                  }}
                  onBlur={() => {
                    handleInputBlur('userType')
                  }}
                >
                  <input
                    type="radio"
                    {...register("userType")}
                    value="SELLER"
                    className={styles.hiddenRadio}
                    onChange={(e) => {
                      register('userType').onChange(e)
                    }}
                  />
                  SELLER
                </label>
              </div>
            </span>

          </div>
          {/* <CustomMenu
          keyHandler={() => { handleInputFocus('userType'); setIsDropdownOpen(true) }}
          control={control}
          name={'userType'}
          placeholder={'Choose User Type'}
          MenuProps={{
            classes: {
              paper: clsx(styles.Dropdownpaper, 'selectUserType'),
              list: styles.muiMenuList,
            },
          }}
          onOpen={() => { setIsDropdownOpen(true) }}
          onClick={() => {
            handleInputFocus('userType')
            setIsDropdownOpen(true)
          }}
          onClose={() => {
            handleInputBlur('userType')
            setIsDropdownOpen(false)
          }}
          category={'THIS CANNOT BE CHANGED...CHOOSE WISELY.'}
          items={userTypes.map(userType => ({ title: userType.name, value: userType.value }))}
          className={clsx('selectDropdown1', isDropdownOpen ? 'dropdownFocused' : '')}
        /> */}
          <div >
            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label htmlFor="companyName" className={clsx(isInputFocused.CompanyName && styles.focusLbl)}>COMPANY NAME</label>
              </span>
              <span className={clsx(styles.col1, (isFocused && companyNameInput.length > 1) && openCompanyName && styles.bgAutoComplete)}>
                <Controller
                  name='companyName'
                  control={control}
                  render={({ field: { ...rest } }) => (
                    <Autocomplete
                      freeSolo
                      id={`combo-box-demo`}
                      open={(isFocused && companyNameInput.length > 1) && openCompanyName}
                      onOpen={() => {
                        setOpenCompanyName(true)
                        if (companyNameValue) setIsSelectedCompanyName(true)
                      }}
                      onClose={(e) => {
                        setOpenCompanyName(false)
                      }}
                      options={onboardCompanies?.length ? onboardCompanies : []}
                      value={companyNameValue}
                      inputValue={companyNameInput}
                      className={clsx(styles.inputOnboarding1)}
                      onInputChange={(event, value, reason) => {
                        if (reason !== "reset" || value.length > 0) {
                          setCompanyNameInput(value)
                          setValue('companyName', value, { shouldDirty: true })
                        }
                        if (!value) {
                          setCompanyNameSuggestedText("")
                        }
                      }}
                      disablePortal={true}
                      classes={{
                        root: styles.autoCompleteDesc,
                        popper: styles.autocompleteDescPanel,
                        paper: styles.autocompleteDescInnerPanel,
                        listbox: styles.listAutoComletePanel,
                      }}
                      getOptionLabel={(item) => {
                        return item.company_name ? item.company_name : "";
                      }}
                      filterOptions={(options, state) => {
                        const filtered = options.filter((option) => option.company_name.toLowerCase().startsWith(state.inputValue.toLowerCase()));
                        setCompanyNameSuggestedText(filtered[0]?.company_name);
                        return filtered;
                      }}
                      renderOption={(props, item: any) => <span {...props} company_id={item.id}>{item.company_name ? item.company_name : ""}</span>}
                      renderInput={(params) => (
                        <div
                          className={clsx(styles.companyNameInput, errors?.companyName && styles.error)}
                          ref={params.InputProps.ref}
                        >
                          <span className={styles.inputWrapper}>
                            <input
                              type="text"
                              {...params.inputProps}
                              className={clsx(styles.poDescription)}
                              tabIndex={2}
                              onKeyDown={(e) => {
                                if (e.key === 'Tab' && companyNameSuggestedText?.length > 0) {
                                  setCompanyNameInput(companyNameSuggestedText);
                                  setValue('companyName', companyNameSuggestedText, { shouldDirty: true });
                                  setCompanyNameValue(companyNameSuggestedText);
                                  rest.onChange(companyNameSuggestedText);
                                  requestAnimationFrame(measureInputWidth);
                                }
                              }}
                              style={{
                                width: (!isFocused || companyNameInput.length === 0)
                                  ? '100%'
                                  : `${companyNameInput.length + 0.5}ch`,
                              }}
                              onFocus={() => {
                                setIsFocused(true);
                              }}
                              onBlur={() => {
                                setIsFocused(false);
                                setCompanyNameSuggestedText('')
                              }}
                            />
                            {!!(companyNameSuggestedText?.length !== 0) &&
                              <span className={styles.autoSuggestionText}>
                                {companyNameSuggestedText?.slice(companyNameInput?.length)}
                              </span>
                            }
                          </span>


                        </div>
                      )}

                      onChange={(event, item, reason) => {
                        if (reason === "selectOption") {
                          setCompanyNameInput(item?.company_name)
                          setValue('companyName', item?.company_name, { shouldDirty: true })
                          setCompanyNameSuggestedText('');
                        }
                        setCompanyNameValue(item)
                        rest.onChange(item?.company_name ?? companyNameInput);
                      }}
                      onBlur={(e) => {
                        if (/<|>/g.test(e.target.value) || e.target.value.length === 0) {
                          setError("companyName", { message: 'Company Name is not valid' })
                        } else {
                          clearErrors('companyName')
                        }
                        rest.onBlur(e);
                        handleInputBlur('CompanyName')
                        setIsCompanyDropdownOpen(false)
                      }}
                      onFocus={() => {
                        handleInputFocus('CompanyName')
                        setIsCompanyDropdownOpen(true)
                      }}
                    />
                  )}
                />
              </span>
            </div>

            {/* <div  className={clsx(styles.inputOnboarding1)}>
            <Controller
            name="companyEntity"
            control={control}
            render={({ field: { ...rest } }) => (
                <Autocomplete
                  disabled={disableCompanyEntity}
                  className={clsx(styles.inputOnboarding1)}
                  classes={{
                    root: styles.autoCompleteDesc,
                    popper: styles.autocompleteDescPanel,
                    paper: styles.autocompleteDescInnerPanel,
                    listbox: styles.listAutoComletePanel,
                  }}
                  id={`combo-box-demo1`}
                  freeSolo
                  value={companyEntityValue}
                  inputValue={companyEntityInput}
                  onChange={(event, value) => {
                    setCompanyEntityValue(value);
                    rest.onChange(value ?? null);
                  }}
                  onInputChange={(event, newInputValue) => {
                    setCompanyEntityInput(newInputValue);
                      setValue("companyEntity", newInputValue, { shouldDirty: true });
                  }}
                  onBlur={(e) => {
                    if (/<|>/g.test(e.target.value) || e.target.value.length === 0) {
                      setError("companyEntity", { message: 'Company Entity is not valid' })
                    } else {
                      clearErrors('companyEntity')
                    }
                    rest.onBlur(e);
                    handleInputBlur('CompanyEntity')
                    setIsCompanyDropdownOpen(false)
                  }}
                  onFocus={() => {
                    handleInputFocus('CompanyEntity')
                    setIsCompanyDropdownOpen(true)
                  }}
                  disablePortal={true}
                  options={onboardCompanyEntities?.length ? onboardCompanyEntities :[]}
                  renderInput={(params) => (
                    <div className={clsx(styles.companyNameInput, errors?.companyEntity && styles.error)} ref={params.InputProps.ref}>
                      <input
                        type="text"
                        {...params.inputProps}
                        placeholder="Your Company Entity/Location"
                        className={clsx(styles.poDescription)}
                      />
                    </div>
                  )}
                  getOptionLabel={(item) => {
                      return item ?? "";
                  }}
                />
                )}
            />
          </div> */}
            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.ZipCode && styles.focusLbl)} htmlFor="zipCode">ENTER ZIP CODE</label>
              </span>
              <span className={styles.col1}>
                <Tooltip
                  title={errors?.zipCode?.message}
                  arrow
                  placement={"top"}
                  disableInteractive
                  TransitionComponent={Fade}
                  TransitionProps={{ timeout: 200 }}
                  classes={{
                    tooltip: "inputQtyTooltip",
                  }}
                >
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.zipCode && styles.error)}
                      type='text'
                      register={register("zipCode")}
                      placeholder=''
                      onBlur={(e) => {
                        register("zipCode").onBlur(e);
                        handleInputBlur('ZipCode')
                      }}
                      onChange={(e) => {
                        register("zipCode").onChange(e);
                        const zipCode = e.target.value.replace(/\D/g, '');
                        setValue('zipCode', zipCode);
                      }}
                      onFocus={() => handleInputFocus('ZipCode')}
                      maxLength={5}
                      errorInput={errors?.zipCode}
                      tabIndex={3}
                    />
                  </InputWrapper>
                </Tooltip>
              </span>
            </div>

            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.FirstName && styles.focusLbl)} htmlFor="firstName">FIRST NAME</label>
              </span>
              <span className={styles.col1}>
                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.firstName && styles.error)}
                    type='text'
                    register={register("firstName")}
                    placeholder=''
                    onBlur={(e) => {
                      register("firstName").onBlur(e);
                      handleInputBlur('FirstName')
                    }}
                    onFocus={() => handleInputFocus('FirstName')}
                    errorInput={errors?.firstName}
                    tabIndex={4}
                  />
                </InputWrapper>
              </span>

            </div>

            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.LastName && styles.focusLbl)} htmlFor="lastName">LAST NAME</label>
              </span>
              <span className={styles.col1}>
                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.lastName && styles.error)}
                    type='text'
                    register={register("lastName")}
                    placeholder=''
                    onBlur={(e) => {
                      register("lastName").onBlur(e);
                      handleInputBlur('LastName')
                    }}
                    onFocus={() => handleInputFocus('LastName')}
                    errorInput={errors?.lastName}
                    tabIndex={5}
                  />
                </InputWrapper>
              </span>
            </div>

            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.EmailAddress && styles.focusLbl)} htmlFor="emailAddress">EMAIL ADDRESS</label>
              </span>
              <span className={styles.col1}>


                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.emailAddress && styles.error)}
                    type='text'
                    register={register("emailAddress")}
                    placeholder=''
                    onBlur={(e) => {
                      register("emailAddress").onBlur(e);
                      handleInputBlur('EmailAddress')
                      handleEmailBlur(e, 'emailAddress')
                    }}
                    onFocus={() => handleInputFocus('EmailAddress')}
                    errorInput={errors?.emailAddress}
                    tabIndex={6}
                  />
                </InputWrapper>
              </span>
            </div>

            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.ReEnterEmailAddress && styles.focusLbl)} htmlFor="reEnterEmailAddress">RE-ENTER EMAIL ADDRESS</label>
              </span>
              <span className={styles.col1}>
                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.reEnterEmailAddress && styles.error)}
                    type='text'
                    register={register("reEnterEmailAddress")}
                    placeholder=''
                    onBlur={(e) => {
                      register("reEnterEmailAddress").onBlur(e);
                      handleInputBlur('ReEnterEmailAddress')
                      handleEmailBlur(e, 'reEnterEmailAddress')
                    }}
                    onFocus={() => handleInputFocus('ReEnterEmailAddress')}
                    errorInput={errors?.reEnterEmailAddress}
                    tabIndex={7}
                  />
                </InputWrapper>
              </span>
            </div>

            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.password && styles.focusLbl)} htmlFor="password">CREATE PASSWORD</label>
              </span>
              <span className={styles.col1}>

                <div className={clsx(styles.confirmPasswordInput, (isInputFocused.password) && styles.focusPass, (isInputFocused.confirmPassword) && styles.bgRemove)}>

                  <InputWrapper>
                    <CustomPasswordField
                      onChange={(e) => {
                        e.target.value = e.target.value.trim();
                        register('password').onChange(e);
                      }}
                      placeholder={''}
                      classNameWrapper={styles.passwordInput}
                      isConfirmPassword={true}
                      register={register("password")}
                      currentText={watch('password')}
                      targetText={watch('confirmPassword') ?? ''}
                      onFocus={() => handleInputFocus('password')}
                      onBlur={(e) => {
                        register("password").onBlur(e);
                        handleInputBlur('password')
                        handlePasswordBlur()
                      }}
                      tabIndex={8}
                    />
                  </InputWrapper>
                  {/* <input {...register("password")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password1 ? 'password' : 'text'} placeholder='Password'
                    onFocus={() => handleInputFocus('password')}
                    onChange={(e) => {
                      e.target.value = e.target.value.trim();
                      register("password").onChange(e);
                      setValue('password', e.target.value);
                    }}
                    onBlur={(e) => {
                      register("password").onBlur(e);
                      handleInputBlur('password')
                      handlePasswordBlur()
                    }} />
                  <button className={clsx(styles.showPassBtn, (errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password1')}>
                    {passwordVisibility.password1 ? <ShowPassIcon /> : <HidePassIcon />}
                  </button> */}
                </div>
              </span>
            </div>

            <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.confirmPassword && styles.focusLbl)} htmlFor="confirmPassword">RE-ENTER PASSWORD</label>
              </span>
              <span className={styles.col1}>

                <div className={clsx(styles.confirmPasswordInput, (isInputFocused.confirmPassword) && clsx(styles.focusPass, styles.focusPass2), (isInputFocused.password) && styles.bgRemove)}>
                  <InputWrapper>
                    <CustomPasswordField
                      onChange={(e) => {
                        e.target.value = e.target.value.trim();
                        register('confirmPassword').onChange(e);
                      }}
                      placeholder={''}
                      classNameWrapper={clsx(styles.passwordInput, styles.passwordInput2)}
                      isConfirmPassword={true}
                      register={register("confirmPassword")}
                      currentText={watch('confirmPassword')}
                      targetText={watch('password') ?? ''}
                      onBlur={(e) => {
                        register("confirmPassword").onBlur(e);
                        handleInputBlur('confirmPassword')
                        handlePasswordBlur()
                      }}
                      onFocus={() => handleInputFocus('confirmPassword')}
                      tabIndex={9}
                    />
                  </InputWrapper>
                  {/* <input {...register("confirmPassword")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password2 ? 'password' : 'text'} placeholder='Confirm Password'
                  onFocus={() => handleInputFocus('confirmPassword')}
                  onChange={(e) => {
                    e.target.value = e.target.value.trim();
                    register("confirmPassword").onChange(e);
                    setValue('confirmPassword', e.target.value);
                  }}
                  onBlur={(e) => {
                    register("confirmPassword").onBlur(e);
                    handleInputBlur('confirmPassword')
                    handlePasswordBlur()
                  }} />
                <button className={clsx(styles.showPassBtn,(errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password2')}>
                  {passwordVisibility.password2 ? <ShowPassIcon /> : <HidePassIcon />}
                </button> */}
                  {(isInputFocused.password || isInputFocused.confirmPassword) &&
                    <div className={clsx(styles.passwordRequirements, (isInputFocused.confirmPassword) && styles.passwordRequirements2)}>
                      <span className={clsx(styles.passwordRequirementItem, watch('password')?.length >= 8 && styles.passwordRequirementItemActive)}> 8 Characters Min </span>
                      <span className={clsx(styles.passwordRequirementItem, /[A-Z]/.test(watch('password') || '') && styles.passwordRequirementItemActive)}> 1 Uppercase </span>
                      <span className={clsx(styles.passwordRequirementItem, /[0-9!@#$%^&*(),.?":{}|<>]/.test(watch('password') || '') && styles.passwordRequirementItemActive)}> 1 non-Letter </span>
                    </div>
                  }
                </div>
              </span>

            </div>

            {/* <div className={clsx(styles.inputOnboarding, styles.inputOnboarding1)}>
            <input className={clsx(styles.input1, errors?.firstName && styles.error)}
              {...register("firstName")}
              placeholder='First Name'
              onFocus={() => handleInputFocus('FirstName')}
              onBlur={(e) => {
                register("firstName").onBlur(e);
                handleInputBlur('FirstName')
              }} />

            <input className={clsx(styles.input1, errors?.lastName && styles.error)}
              {...register("lastName")}
              placeholder='Last Name'
              onFocus={() => handleInputFocus('LastName')}
              onBlur={(e) => {
                register("lastName").onBlur(e);
                handleInputBlur('LastName')
              }}
            />
          </div>

          <div className={styles.emailErrorContainer}>
            <Tooltip
              title={errors?.emailAddress?.message || errors?.reEnterEmailAddress?.message}
              arrow
              placement={"top"}
              disableInteractive
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              classes={{
                tooltip: "inputQtyTooltip",
              }}
            >
              <input {...register("emailAddress")} className={clsx(styles.inputOnboarding, (errors?.emailAddress || errors?.reEnterEmailAddress) && styles.error)} type='text' placeholder='Email Address'
                onFocus={() => handleInputFocus('EmailAddress')}
                onBlur={(e) => {
                  register("emailAddress").onBlur(e);
                  handleInputBlur('EmailAddress')
                  handleEmailBlur(e)
                }} />
            </Tooltip>

            <input {...register("reEnterEmailAddress")} className={clsx(styles.inputOnboarding, (errors?.emailAddress || errors?.reEnterEmailAddress) && styles.error)} type='text' placeholder='Re-Enter Email Address'
              onFocus={() => handleInputFocus('ReEnterEmailAddress')}
              onBlur={(e) => {
                register("reEnterEmailAddress").onBlur(e);
                handleInputBlur('ReEnterEmailAddress');
                handleEmailBlur(e);
              }} />
            {(errors?.emailAddress || errors?.reEnterEmailAddress) &&
              <div className={styles.errorBorder}>
                <ErrorEmailIcon />
              </div>
            }
           
           <div className={styles.passwordErrorContainer}>
         
              <div className={styles.confirmPasswordInput}>
                <Tooltip
                  title={errors?.password?.message || errors?.confirmPassword?.message}
                  arrow
                  placement={"top"}
                  disableInteractive
                  TransitionComponent={Fade}
                  TransitionProps={{ timeout: 200 }}
                  classes={{
                    tooltip: "inputQtyTooltip",
                  }}
                >
                  <input {...register("password")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password1 ? 'password' : 'text'} placeholder='Password'
                    onFocus={() => handleInputFocus('password')}
                    onChange={(e) => {
                      e.target.value = e.target.value.trim();
                      register("password").onChange(e);
                      setValue('password', e.target.value);
                    }}
                    onBlur={(e) => {
                      register("password").onBlur(e);
                      handleInputBlur('password')
                      handlePasswordBlur()
                    }} />
                  <button className={clsx(styles.showPassBtn, (errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password1')}>
                    {passwordVisibility.password1 ? <ShowPassIcon /> : <HidePassIcon />}
                  </button>
                </Tooltip>
              </div>

              <div className={styles.confirmPasswordInput}>
                <input {...register("confirmPassword")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password2 ? 'password' : 'text'} placeholder='Confirm Password'
                  onFocus={() => handleInputFocus('confirmPassword')}
                  onChange={(e) => {
                    e.target.value = e.target.value.trim();
                    register("confirmPassword").onChange(e);
                    setValue('confirmPassword', e.target.value);
                  }}
                  onBlur={(e) => {
                    register("confirmPassword").onBlur(e);
                    handleInputBlur('confirmPassword')
                    handlePasswordBlur()
                  }} />
                <button className={clsx(styles.showPassBtn,(errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password2')}>
                  {passwordVisibility.password2 ? <ShowPassIcon /> : <HidePassIcon />}
                </button>
              </div>
                 {(errors?.password || errors?.confirmPassword) &&
                        <div className={styles.errorBorder}>
                            <ErrorEmailIcon />
                        </div>
                    }
           </div>
             
           

            <Tooltip
              title={errors?.zipCode?.message}
              arrow
              placement={"top"}
              disableInteractive
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              classes={{
                tooltip: "inputQtyTooltip",
              }}
            >

              <input {...register("zipCode")} className={clsx(styles.inputOnboarding, errors?.zipCode && styles.error)} type='tel' placeholder='Enter your zip code'
                onFocus={() => handleInputFocus('ZipCode')}
                onChange={(e) => {
                  register("zipCode").onChange(e);
                  const zipCode = e.target.value.replace(/\D/g, '');
                  setValue('zipCode', zipCode);
                }}
                maxLength={5}
                onBlur={(e) => {
                  register("zipCode").onBlur(e);
                  handleInputBlur('ZipCode')
                }} />
            </Tooltip>

          </div> */}
          </div>

        </div>

        <div className={styles.btnFooterTnc}>
          {/* <div className='bgEllips'></div>
            <div className='bgEllips1'></div> */}
          <div className={styles.alreadyAccountLogin}>Already have an account? <span onClick={() => navigate(routes.loginPage)}>Login</span></div>
          <div><button tabIndex={10} className={clsx(styles.nextTncBtn, isTrialDialogOpen && styles.buttonNone)} disabled={!(Object.keys(errors).length === 0 && isValid && watch('reEnterEmailAddress')?.length !== 0 && watch('emailAddress')?.length !== 0)} onClick={handleSubmit(onSubmit)}><span>Next</span></button></div>

        </div>

      </div>
    
            
      {/* <div className={clsx(styles.btnSection, (Object.keys(errors).length === 0 && isValid) ? styles.isEnabled : '')}>
        <button className={clsx(styles.nextBtn, (Object.keys(errors).length === 0 && isValid) && styles.enableBtn)} disabled={!(Object.keys(errors).length === 0 && isValid)}
          onClick={handleSubmit(onSubmit)}> Next </button>
      </div>

      <div className={clsx(styles.loginBtnSection, (Object.keys(errors).length === 0 && isValid) ? styles.isEnabled : '')}>
        Already have an account? <button onClick={() => navigate(routes.loginPage)}>Login</button>
      </div> */}


      {/* <OnboardingFooter /> */}
      <Dialog
        open={isTrialDialogOpen}
        onClose={handleTrialDialogClose}
        transitionDuration={200}
        container={formContainerRef.current}
        disableBackdropClick={true}
        disableBackdropTransition={true}
        hideBackdrop={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)'
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
            maxHeight: '91%',
          }
        }}
        classes={{
          root: styles.ErrorDialog,
          paper: styles.dialogContainer
        }}
      >
        <div className={styles.dialogHeader}>
          <p>ENJOY YOUR 30-DAY</p>
          <p className={styles.dialogHeaderFreeTrial}>FREE TRIAL</p>
        </div>
        <div className={styles.dialogContent}>
          <p><span className={styles.bold}>No payment info needed</span> during the 30-day full-featured free trial that you're about to join. </p>
          <p>
            The subscription is only $50 per month, per user.  In time savings, alone, that's a no-brainer. <br />
            Best practice is to subscribe before your trial expires, for uninterrupted access.
          </p>
          <p>If you have not subscribed and the trial has expired you will be using our free version.</p>
          <p>Subscribe at any time.</p>
        </div>
        <div className={styles.dialogFooter}>
          <button onClick={handleTrialDialogConfirm}>GOT IT - NEXT</button>
        </div>
      </Dialog>
    </div>
  );
}
export default OnboardingDetails;
