.subscribeAdminPage {
  width: 800px;
  height: 1000px;
  background: url(../../assets/New-images/AppBG1.svg) no-repeat;
  background-position: bottom;

  .subscribeAdminPageHeader {
    padding: 32px 0px 48px 0px;
    font-family: Inter;
    font-size: 32px;
    font-weight: bold;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: -0.64px;
    text-align: center;
    color: #fff;
    width: 100%;

    span {
      font-family: Inter;
      font-size: 32px;
      font-weight: bold;
      font-stretch: normal;
      line-height: normal;
      letter-spacing: -0.64px;
      text-align: left;
      color: #8dffae;
      padding: 0px 6px;
    }
  }

  .subscribeAdminPageButton {
    border-radius: 10px;
    background-color: #222329;
    width: 704px;
    height: 78px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 48px;

    span {
      font-family: Syncopate;
      font-size: 32px;
      font-weight: bold;
      line-height: 1.3;
      letter-spacing: -1.28px;
      text-align: center;
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .subscribeAdminPageContent {
    padding-left: 83px;
    padding-right: 40px;

    .subscribeAdminPageContentHeader {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: bold;
      line-height: 1;
      letter-spacing: 0.98px;
      color: #9b9eac;
      text-align: center;
      display: flex;

      span:nth-child(1) {
        width: 144px;
      }

      span:nth-child(2) {
        width: 115px;
      }

      span:nth-child(3) {
        width: 195px;
      }

      span:nth-child(4) {
        width: 101px;
      }

      span:nth-child(5) {
        width: 114px;
      }
    }

    .subscribeAdminPageContentBody {
      display: flex;
      flex-direction: column;
      gap: 13px;
      margin-top: 16px;

      .focusedContainer {
        &.subscribeAdminPageContentBodyItem {
          // box-shadow: inset 4px 4px 4px 0 #000;
          // background-color: rgba(255, 255, 255, 0.04);
          background: url(../../assets/New-images/Input-Active.svg) no-repeat;
          background-size: cover;
          background-position: center;
          // .nameInput,.inputGroup,.inputEmail,.inputStatus{
          //   border-right: 1px solid  rgba(255, 255, 255, 0.1);
          // }
        }
      }

      .subscribeAdminPageContentBodyItem {
        border: 1px solid transparent;
        width: 100%;
        border-radius: 5px;
        background-color: rgba(255, 255, 255, 0.1);
        height: 40px;
        position: relative;
        display: flex;
        align-items: center;

        .lblList {
          font-family: Syncopate;
          font-size: 14px;
          font-weight: bold;
          line-height: 1;
          letter-spacing: 0.98px;
          text-align: left;
          color: #9b9eac;

          &.lblName {
            width: 143px;
            padding: 0px 10px 0px 30px;
          }

          &.lblGroup {
            width: 114px;
            padding: 0px 10px 0px 12px;
          }

          &.lblEmail {
            width: 194px;
            padding: 0px 10px 0px 12px;
          }

          &.lblStatus {
            width: 100px;
            padding: 0px 10px 0px 12px;
          }

          &.lblAction {
            width: 114px;
            padding: 0px 10px 0px 12px;
          }
        }



        .nameInput,.inputEmail {
          width: 143px;
          height: 100%;

          input {
            width: 100%;
            height: 100%;
            background-color: transparent;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            letter-spacing: -0.7px;
            text-align: left;
            color: #fff;
            border: 0px;
            box-shadow: none;
            padding: 0px 10px 0px 30px;

            &:focus {
              outline: none;
              color: #16b9ff;
            }
          }
        }

        .inputGroup {
          height: 100%;
          width: 114px;
          padding: 0px 10px 0px 12px;
        }

        .inputEmail {
          width: 194px;
        }

        .inputStatus {
          width: 100px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0px 10px 0px 12px;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: normal;
          letter-spacing: -0.7px;
          text-align: center;
          color: #fff;
        }



        .subscribeAdminPageContentBodyItemNumber {
          left: -38px;
          top: 14px;
          font-family: Syncopate;
          font-size: 16px;
          font-weight: bold;
          line-height: 1;
          letter-spacing: 1.12px;
          text-align: left;
          color: #9b9eac;
          position: absolute;
          padding-right: 28px;
        }
      }
    }
  }

}

.dropdownFormControl{
  height: 100%;
}

.dropdownMain.dropdownMain {
  width: 114px;
  height: 100%;

  &.groupDropdownMain{
    width: 100%;
  }

  svg {
    width: 24px;
    height: 24px;
    right: 0px;
    transform: unset;

    path {
      fill: #fff;
    }
  }

  fieldset {
    border: unset;
    border-color: transparent;
  }

  .selectDropdown.selectDropdown {
    background-color: transparent;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: -0.7px;
    text-align: left;
    color: rgba(255, 255, 255);
    padding: 0px 20px 0px 12px;
  }
}

.selectDropdownList.selectDropdownList {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 4px;
  border-radius: 6px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(128, 130, 140, 0.28);
  margin-top: 3px;
  margin-left: 12px;
  box-shadow: unset;

  ul {
    width: 100%;
    padding: 0px;

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      padding: 6px;

      &[aria-selected="true"] {
        background-color: transparent;
        box-shadow: unset;
        color: rgba(255, 255, 255, 0.6);
      }

      &:hover {
        border-radius: 5px;
        font-weight: bold;
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
      }

    }
  }
}