import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller } from 'react-hook-form';
import CustomToggleCheckbox from 'src/renderer2/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import axios from 'axios';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import CustomAddressComponent from '../components/CustomAddressComponent';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { unformatPhoneNumber } from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import StateSelector from '../components/StateSelector/StateSelector';

interface InputFocusState {
  stockingAddress: boolean;
}

const ShipmentsTab: React.FC<{ mainWrapperRef: React.RefObject<HTMLDivElement>, register: any, handleSubmit: any, control: any, watch: any, setValue: any, getValues: any, setError: any, clearErrors: any, errors: any, buyerSettings: any, trigger: any, isDirty: any, resetField: any, dirtyFields: any, setActiveTab: any }> = ({ mainWrapperRef, register, handleSubmit, control, watch, setValue, getValues, setError, clearErrors, errors, trigger, buyerSettings, trigger: any, isDirty, resetField, dirtyFields, setActiveTab }) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState<Array<{ id: number | string, code: string }>>([]);
  const [customAddressComponentOpen, setCustomAddressComponentOpen] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  const [validationInProgress, setValidationInProgress] = useState(true);
  const shipmentPopupRef = useRef(null);
  const { mutate: saveUserSettings } = useSaveUserSettings();
  const addressParts = [
    watch("stockingAddress.line1"),
    watch("stockingAddress.line2"),
    watch("stockingAddress.city"),
    watch("stockingAddress.stateCode"),
    watch("stockingAddress.zip")
  ].filter(Boolean);

  useEffect(() => {
    if (userData.data.id && referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData, userData]);

  useEffect(() => {
    const stateField = `stockingAddress.state`;
    const zipField = `stockingAddress.zip`;
    handleStateZipValidation(zipField, stateField);
}, [watch('stockingAddress.zip'), watch('stockingAddress.state')])


  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    stockingAddress: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields[inputName]) {
      if (focusedInput !== 'stockingAddress') {
        setTimeout(() => {
          handleSaveShipmentSettings()
        }, 100)
      }
    }
  }

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        setValidationInProgress(false)
        const payload = {
          data: {
            state_id: getValues(stateCode),
            zip_code: parseInt(getValues(zipCode)),
          },
        };
        const checkStateZipResponse = await axios.post(
          import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
          payload
        );
        if (checkStateZipResponse.data.data === true) {
          clearErrors([stateCode, zipCode]);
          return true
        } else {
          setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
          setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
          return false
        }
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleSaveShipmentSettings = async () => {
    const fieldsToValidate = [];
    const userSettingsPayload: any = {};
    try {

      if (
        watch('stockingAddress.line1') && watch('stockingAddress.line1') !== "" &&
        watch('stockingAddress.city') && watch('stockingAddress.city') !== "" &&
        watch('stockingAddress.state') && watch('stockingAddress.state') !== "" &&
        watch('stockingAddress.zip') && watch('stockingAddress.zip') !== ""
      ) {
        // Check if there are existing errors for buyerAddress
        const hasExistingErrors = errors?.stockingAddress?.line1 ||
          errors?.stockingAddress?.line2 ||
          errors?.stockingAddress?.city ||
          errors?.stockingAddress?.state ||
          errors?.stockingAddress?.zip;

        // Only add to validation if no existing errors (to preserve manual errors)
        if (!hasExistingErrors) {
          fieldsToValidate.push('stockingAddress');
          // Add all fields to the payload
          userSettingsPayload.stocking_address = {
            line1: watch('stockingAddress.line1'),
            line2: watch('stockingAddress.line2') || null,
            city: watch('stockingAddress.city'),
            state_id: watch('stockingAddress.state'),
            zip: watch('stockingAddress.zip')
          }
        }
      }
      userSettingsPayload.order_fulfillment_states = watch('orderFulfillmentStates');
      fieldsToValidate.push('orderFulfillmentStates');
      userSettingsPayload.order_claim_preferences = Boolean(watch('orderClaimPreferences'));
      fieldsToValidate.push('orderClaimPreferences');

      if (fieldsToValidate.length > 0) {
        const isValid = await trigger(fieldsToValidate);
        if (isValid && Object.keys(userSettingsPayload).length > 0) {
          saveUserSettings({ route: 'user/seller/settings/shipment', data: userSettingsPayload });
          // Reset dirty state for successfully validated and saved fields
          fieldsToValidate.forEach((fieldName) => {
            const currentValue = watch(fieldName);
            resetField(fieldName, {
              defaultValue: currentValue,
              keepError: false,
              keepDirty: false,
              keepTouched: true
            });
          });
        }
      }
    } catch (err) {
      console.error(err)
    }
  }

  const handleCheckBoxKeyDown = (e: any) => {
    if (e.key === 'Tab') {
      if (!watch('orderClaimPreferences')) {
        setActiveTab('PAYMENTS');
      }
    }
  }

  const handleMainLocationAddressClick = () => {
    setFocusedInput('stockingAddress');
    setCustomAddressComponentOpen(true);

  }

  const handleMainLocationAddressKeyDown = (e: React.KeyboardEvent<HTMLSpanElement>) => {
    if (e.key === 'Enter') {
      handleMainLocationAddressClick();
    }
  }

  const handleCustomAddressComponentClose = async () => {
    setCustomAddressComponentOpen(false);
    if (focusedInput && dirtyFields[focusedInput]) {
      if (focusedInput === 'stockingAddress') {
        // Only validate if both zip and state have values
        const zipValue = watch(`${focusedInput}.zip`);
        const stateValue = watch(`${focusedInput}.state`);
        if (zipValue && stateValue) {
          const isZipCodeVaild = await handleStateZipValidation(`${focusedInput}.zip`, `${focusedInput}.state`)
          if (isZipCodeVaild) {
            setTimeout(() => {
              handleSaveShipmentSettings();
            }, 100)
          }
        } else {
          setTimeout(() => {
            handleSaveShipmentSettings();
          }, 100)
        }
      }
    }
    setFocusedInput(null);
  }

  const handleSelectAllStates = () => {
    setValue('orderFulfillmentStates', States.map(s => s.id), { shouldDirty: true });
    handleSaveShipmentSettings();
  }

  const handleDeselectAllStates = () => {
    setValue('orderFulfillmentStates', [], { shouldDirty: true });
    handleSaveShipmentSettings();
  }

  return (
    <div className={clsx(styles.tabContent, styles.tabContentShipment)} ref={shipmentPopupRef}>
      <div className={styles.scrollerContainer}>
        <div className={styles.formContainer}>

          <div className={clsx(styles.formGroupInput, styles.deliveryAddressContainer)}>
            <span className={styles.col1}>
              <label htmlFor="stockingAddress">
                MAIN STOCKING LOCATION
              </label>
            </span>
            <span className={styles.col1}>
              {
                <span
                  onFocus={() => {
                    setIsInputFocused((prevState) => ({
                      ...prevState,
                      stockingAddress: true,
                    }));
                  }}
                  onBlur={() => {
                    setIsInputFocused((prevState) => ({
                      ...prevState,
                      stockingAddress: false,
                    }));
                  }}
                  tabIndex={0} onClick={handleMainLocationAddressClick} onKeyDown={handleMainLocationAddressKeyDown} className={clsx(styles.inputCreateAccount, (errors.stockingAddress) && styles.error)}>
                  <span className={clsx(styles.locationAdressPreviewContainer, addressParts.length <= 0 && styles.placeHolderAddress)}>
                    {(() => {
                      return addressParts.length > 0
                        ? addressParts.join(", ")
                        : "123 Main St, Houston, TX 77077";
                    })()}
                  </span>
                </span>
              }

            </span>
          </div>
          <div className={clsx(styles.formGroupInput, styles.whereCanYouFulfillOrders)}>
            <div className={styles.whereCanYouFulfillOrdersHeader}>
              <span className={styles.col1}>
                <label>
                  WHERE CAN YOU FULFILL ORDERS?
                </label>
              </span>
              <div className={styles.stateDropdownContainer}>
                <div className={styles.selectTabLabel}>Select the states that apply to your delivery area.</div>
                <div className={styles.radioGroupContainer}>
                  <button type="button" className={clsx(styles.radioButtonLeft, styles.radioButton)} onClick={handleSelectAllStates} disabled={watch('orderFulfillmentStates')?.length === States.length}>Select All</button>
                  <button type="button" className={clsx(styles.radioButtonRight, styles.radioButton)} onClick={handleDeselectAllStates} disabled={watch('orderFulfillmentStates')?.length === 0}>Deselect All</button>
                </div>
              </div>
            </div>
            <div className={styles.stateSelectorContainer}>
              <Controller
                name="orderFulfillmentStates"
                control={control}
                render={({ field }) => (
                  <StateSelector
                    allStates={States}
                    selectedStates={field.value || []}
                    onChange={(newSelection) => {
                      field.onChange(newSelection);
                      setValue('orderFulfillmentStates', newSelection, { shouldDirty: true });
                      handleSaveShipmentSettings();
                    }}
                  />
                )}
              />
            </div>


          </div>

          <div className={clsx(styles.formGroupInput, styles.orderClaimPreferencesMain)}>
            <span className={styles.col1}>
              <label>
                ORDER CLAIM PREFERENCES
              </label>
              <span className={styles.col1}>

              </span>
            </span>

            <span className={styles.chkNote}>
              <span className={styles.chkNote1}>
                <CustomToggleCheckbox
                  name="orderClaimPreferences"
                  control={control}
                  onKeyDown={handleCheckBoxKeyDown}
                  onChange={
                    (e: any) => {
                      setValue('orderClaimPreferences', e);
                      handleSaveShipmentSettings();
                    }
                  }
                />
                This setting will apply your selected locations to your Available Orders screen. You will only see
              </span>

              <span>
                 Purchase Orders destined for delivery to your selected locations above. Select 'Yes' to apply this setting.
              </span>

            </span>
          </div>
        </div>
      </div>
      <Dialog
        open={customAddressComponentOpen && !!focusedInput}
        onClose={(event) => handleCustomAddressComponentClose()}
        transitionDuration={100}
        disableScrollLock={true}
        container={shipmentPopupRef.current}

        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0
          }
        }}
        hideBackdrop
        classes={{
          root: styles.customeAddressPopup,
          paper: styles.dialogContent
        }}
      >
        <button className={styles.closeIcon} onClick={(event) => handleCustomAddressComponentClose()}><CloseIcon /></button>
        <CustomAddressComponent
          focusedInput={focusedInput}
          States={States}
          register={register}
          handleInputBlur={handleInputBlur}
          handleInputFocus={handleInputFocus}
          errors={errors}
          control={control}
          setValue={setValue}
          setCustomAddressComponentOpen={setCustomAddressComponentOpen}
          setFocusedInput={setFocusedInput}
        />
      </Dialog>
    </div>
  );
};

export default ShipmentsTab;


