// @ts-nocheck
import { useEffect, useRef } from "react";
import styles from './onboardingWelcome.module.scss';
// import video1 from '../../assets/images/welcomepage.mp4';   
import video2 from '../../assets/New-images/Create-Account/giss_2.0_onboard_video_2160.mp4';
import { useNavigate } from "react-router";
import { routes } from "../../common";
import clsx from "clsx";
import { useGlobalStore } from "@bryzos/giss-ui-library";

function OnboardingWelcome() {
    const videoRef = useRef();
    const navigate = useNavigate();
    const {setShowLoader, isAppReadyToUseInState } = useGlobalStore()

    useEffect(()=>{
        if(isAppReadyToUseInState)
       setShowLoader(false) 
    },[isAppReadyToUseInState])

    useEffect(() =>{
        function goToMainPage(){
            navigate(routes.onboardingDetails);
        }
  
        document.addEventListener('keydown',goToMainPage)
        document.addEventListener('click',goToMainPage)
        return(()=>{
          document.removeEventListener('keydown',goToMainPage)
          document.removeEventListener('click',goToMainPage)
        })
      },[navigate])
    const handleVideoEnded = () => {
        navigate(routes.onboardingDetails);
    }
    if (videoRef.current) {
        videoRef.current.addEventListener('ended', handleVideoEnded);
    }
    return (
        <div className={styles.welcomePageContainer}>
            <div className={clsx(styles.welcomePage, 'bgBlurContent')}>
                <video src={video2}  autoPlay={true} ref={videoRef}/>
            </div>
        </div>
    );
}
export default OnboardingWelcome;
