import { AuthError, reactQueryKeys, routes } from '../common';
import { useEventListener } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from "yup";
import { useEffect, useRef, useState } from 'react';
import { OTPInputComponent } from '../component/OTPInput';
import { emojiRemoverRegex, useGlobalStore, getChannelWindow, useAuthStore } from '@bryzos/giss-ui-library';
import { useNavigate } from 'react-router';
import useOnSubmitLogin from '../hooks/useOnSubmitLogin';
import InputWrapper from '../component/InputWrapper';
import CustomTextField from '../component/CustomTextField';
import CustomPasswordField from '../component/CustomPasswordField';
import { navigatePage } from '../helper';
import { useLeftPanelStore } from '../component/LeftPanel/LeftPanelStore';

const Login = () => {
    const channelWindow =  getChannelWindow();
    const navigate = useNavigate();
    const { signupUser, setIsManualLogin, existingUserEmail, setExistingUserEmail, setIsSubscribeClickedDuringSignup } = useGlobalStore();
    const {loginError, setLoginError, submitLogin} = useAuthStore()
    const {resetLeftPanelStore} = useLeftPanelStore()
    const [passwordVisibility, setPasswordVisibility] = useState(true);
    const { register, watch, handleSubmit, setError, clearErrors, setValue, formState: { errors, isValid } } = useForm({
        resolver: yupResolver(
            yup.object({
                email: yup.string().required('Email is required'),
                password: yup.string().trim().min(6).required()
            }).required()
        ),
        mode: 'onSubmit',
    });
    const onSubmitLogin =  useOnSubmitLogin(setLoginError);
    const logoRef = useRef<HTMLButtonElement>(null);
    const passwordRef = useRef<HTMLButtonElement>(null);

    const password = watch('password');

    const [showPasswordInput, setShowPasswordInput] = useState(false);
    const [emailError, setEmailError] = useState<boolean>(false);
    const handleFocus = () => {
        setShowPasswordInput(true);
    };

    const handleEmailFocus = () => {
        setShowPasswordInput(false);
    };
    useEffect(()=>{
        resetLeftPanelStore()
        return () => {
            setLoginError()
            setExistingUserEmail(null);
        }
    }, [])

    useEffect(() => {
        if (errors.root?.serverError)
            clearErrors('root.serverError')
    }, [watch('email'), watch('password')])

    useEffect(()=>{
        if(signupUser){
            submitLogin(signupUser)
        }
    },[signupUser])

    useEffect(()=>{
        if(existingUserEmail){
            setValue('email', existingUserEmail);
            passwordRef.current?.focus();
        }
    },[existingUserEmail])

    const handleOnClickLogin = () => {
        logoRef.current?.focus()
        setIsManualLogin(true)
        setIsSubscribeClickedDuringSignup(false);
        handleSubmit(submitLogin)()
    } 

    const togglePasswordVisibility = () => {
        setPasswordVisibility(!passwordVisibility);
      };

    const handleLoginKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.key === 'Enter') {
            handleOnClickLogin()
        }
    }

    const handleJoinBryzosKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();
            navigatePage(location.pathname, { path: routes.onboardingDetails })
        }
    }

    return (
          <>
       
        <div className={'loginWrapper'} onKeyDown={handleLoginKeyDown}>
            <div className='bryzosTitle'>BRYZOS</div>
            <span className='emailInput'>
            <InputWrapper>
                           <CustomTextField type='email' register={register("email")}  autoFocus onFocus={handleEmailFocus}
                            onChange={(e) => setLoginError()}
                            errorInput={loginError === AuthError.EmailNotFound && 'Email not Found'}
                            placeholder='Login Email'
                            setError={setEmailError}
                            className={'loginInput'}
                            tabIndex={1}
                            />
            </InputWrapper>
                 
            </span>
            <span className='togglePassWrapper'>
            <InputWrapper>
                            <CustomPasswordField register={register("password")} placeholder='Password'
                                onChange={(e) => {
                                    setLoginError();
                                    register("password").onChange(e);
                                }} 
                                errorInput={loginError === AuthError.PasswordMismatch && 'Password Miss match'}
                                currentText={password}
                                customPasswordFieldRef={passwordRef}
                                tabIndex={2}
                                />
            </InputWrapper>
                 {(channelWindow?.getLoginCredential && loginError === AuthError.PasswordMismatch) && 
                        <button onClick={()=>navigate(routes.forgotPassword)} className='forgot-password-btn'>Reset Password</button>
                } 

            </span>
            <span className='submitBtn'><button onClick={handleOnClickLogin} disabled={!isValid || !emailError}>Login</button></span>
            <button className={`joinBtn`} onClick={()=>{navigatePage(location.pathname, {path:routes.onboardingDetails})}} onKeyDown={handleJoinBryzosKeyDown} type="button" tabIndex={3}>
                        {existingUserEmail ? 'Sign Up' : 'JOIN BRYZOS'}
            </button>
        </div>
        </>
    );
};

export default Login;