// @ts-nocheck
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router';
import axios from 'axios';
import { fileType, routes } from '../common';
import { useGlobalStore, userRole, getChannelWindow, downloadFilesUsingFetch } from '@bryzos/giss-ui-library';
import { Dialog } from '@mui/material';
import { getHomeRoute } from '../helper';

function Tnc() {
    const {backNavigation, setShowLoader, userData, setDisableBryzosNavigation, setGlobalLogout, referenceData} = useGlobalStore();
    const navigate = useNavigate();
    const channelWindow =  getChannelWindow();
    const [tandCData, setTandCData] = useState({});
    const [, setTncBottom] = useState(false);
    const [tnc, setTnc] = useState('');
    const [openErrorDialog, setOpenErrorDialog] = useState(false);

    const location = useLocation();
    const { isViewMode, navigateTo } = location.state;
    
    useEffect(() => {
        if (!userData.data) {
            navigate(routes.loginPage);
        }
        if(referenceData)
        referenceData?.ref_bryzos_terms_conditions?.forEach(termsAndCondition => {
            if(userData?.data?.type === termsAndCondition?.type){
                setTandCData(termsAndCondition)
            }
        });
    }, [userData,referenceData]);

    const handleLogout = async () => {
      setShowLoader(true);
      setGlobalLogout(routes.loginPage);
    };

    const handleSubmitTnc = () => {
        const payload = {
            data: {
                "bryzos_terms_condtion_id": tandCData.id,
                "terms_conditions_version": tandCData.terms_conditions_version,
                "email_id": userData.data.email_id
            }
        };
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveTermsCondition', payload).then(response => {
            if (response.data !== null) {
                setDisableBryzosNavigation(false);
                if(channelWindow?.getLoginCredential && userData.data.is_migrated_to_password <= 1)
                navigate(routes.changePassword)
                else
                navigate(getHomeRoute());
            }
        })
            .catch(error => { console.error(error) });
    }
    const FetchHtml = async () => {
        const response = await fetch(tandCData.cloudfront_url+"?ver="+new Date().getTime());
        return await response.text();
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
            setShowLoader(false);
        });
    };
    useEffect(() => {
        if(Object.keys(tandCData).length !== 0){
            SetHtml(true)
        }
    },[tandCData]);

    const handleScroll = (e) => {
        const tncWorking = e.target.scrollHeight - e.target.scrollTop
        const bottom =
            Math.floor(tncWorking) === e.target.clientHeight;
        if (bottom) {
            setTncBottom(bottom);
        }
    };
    
    const downloadReports = () => {
        const url = userData.data.type === userRole.sellerUser ? import.meta.env.VITE_FILE_URL_DOWNLOAD_SELLER_TNC_PDF : import.meta.env.VITE_FILE_URL_DOWNLOAD_BUYER_TNC_PDF;
        const showError = downloadFilesUsingFetch(`${url}?ver=${new Date().getTime()}`, 'Tnc', fileType.pdf);
        showError.then(res => {
            if (res) {
                setOpenErrorDialog(false);
            } else {
                setOpenErrorDialog(true);
            }
        })
    }

    return (
        <>
            <div className='widgetBody tncBg'>
                <div className='termsAndConditions'>
                  
                        <div className='TncContainer'>
                            <div className='termsAndConditions' onScroll={handleScroll}
                            >
                                <div>
                                    <div className='tncHead'>
                                        <div>Bryzos Instant Pricing Desktop Widget Terms of Use</div>
                                    </div>
                                    <p className='effectiveDate'>Effective as of: March 8, 2023</p>
                                    <div className='oldTnc'  dangerouslySetInnerHTML={{ __html: tnc }}></div>

                                </div>
                            </div>
                        </div>

                </div>
                <div className='tncButtons'>
                    {isViewMode ?
                        <>
                            <button onClick={()=>navigate(backNavigation)} className='disagreeBtn'>Back</button>
                            <div onClick={() => { downloadReports() }} className='downloadTnCBtn'> Click here to download T&C </div>
                        </>
                        :
                        <>
                        <button onClick={handleLogout} className='disagreeBtn'>Disagree</button>
                        <div onClick={() => { downloadReports() }} className='downloadTnCBtn'> Click here to download T&C </div>
                        <button className='agreeBtn1' onClick={handleSubmitTnc}>Agree</button>
                        </>
                    }

                    
                    
                </div>
            </div>
            <Dialog
                open={openErrorDialog}
                onClose={(event) => setOpenErrorDialog(false)}
                transitionDuration={200}
                hideBackdrop
                classes={{
                    root: 'ErrorDialog',
                    paper: 'dialogContent'
                }}

            >
                <p>No data found. Please try again in sometime</p>
                <button className={'submitBtn'} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
            </Dialog>
        </>
    );
}
export default Tnc;
