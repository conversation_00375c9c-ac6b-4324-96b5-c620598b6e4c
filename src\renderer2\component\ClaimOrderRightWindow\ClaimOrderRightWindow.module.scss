.claimOrderRightWindow {
    width: 322px;
    padding: 20px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    background: url(../../assets/New-images/Create-PO-Order-Ledger.svg) no-repeat transparent;

    .summarySection {
        background: url(../../assets/New-images/MaterialTotal.svg) no-repeat transparent;
        padding: 10px;
        background-size: cover;
    }

    .summaryRow {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;

        &:last-child {
            margin-bottom: 0px;
        }

        .summaryRowLbl {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
        }

        .summaryRowNum {
            font-family: Inter;
            font-size: 18px;
            line-height: 1;
            text-align: right;
            color: #fff;
        }

        &.muted {

            .summaryRowLbl,
            .summaryRowNum {
                color: rgba(255, 255, 255, 0.4);
            }

        }

        &.total {
            padding: 1rem 0;
            font-weight: 600;
            font-size: 1.125rem;
        }
    }

    .totalPurchase {
        width: 100%;
        padding: 6px 10px 10px;
        background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
        border-radius: 0px 0px 13px 13px;
        margin-top: -1px;

        .totalPurchaseLbl {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
        }

        .totalPurchaseNum {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: right;
            color: #fff;
        }
    }


    .claimOrderNote {
        margin: 16px 0px 75px 0px;
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.6;
        letter-spacing: -0.36px;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
    }


    .btnSection {
        display: flex;
        flex-direction: column;
        row-gap: 12px;

        button {
            width: 100%;
            height: 50px;
            flex-grow: 0;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 8px 0 8px;
            border-radius: 10px;
            background-color: #222329;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: rgba(255, 255, 255, 0.4);
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0px;
            }

            &[disabled] {
                background-color: #222329;
                color: rgba(255, 255, 255, 0.1);
            }

            &:not([disabled]):not(.acceptOrderBtn):hover {
                background-color: #fff;
                color: #191a20;
            }

            &.orderPreviewBtn {
                display: flex;
                flex-direction: column;
                font-family: Syncopate;
                font-size: 16px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.64px;
                text-align: center;
                color: #fff;

                span {
                    font-family: Syncopate;
                    font-size: 12px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.2;
                    letter-spacing: 0.48px;
                    text-align: left;
                    color: #ffc44f;
                }
            }
        }
    }

    .acceptOrderBtn.acceptOrderBtn {
        width: 100%;
        height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
        background: url('../../assets/New-images/accept-order.svg');
        background-repeat: no-repeat;
        background-position: center;
        margin-top: -8px;

        &:hover {
            background: url('../../assets/New-images/accept-order-hover.svg');
            background-repeat: no-repeat;
            background-position: center;

        }


    }


}

.blurredContainer {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    position: absolute;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 21px;
}