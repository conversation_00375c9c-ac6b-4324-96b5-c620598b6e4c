//@ts-nocheck
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

class PdfFetcher {

  public static async fetchPdf(url, headersObj, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const axiosOption = { url: url, method: 'POST', responseType: 'arraybuffer', data: data, headers: headersObj };
        axios(axiosOption).then(response => {
          if (response.headers['content-type'].includes("application/pdf") && response.data) {
            resolve(response.data);
          } else {
            reject(new Error("sorry, unable to create pdf."));
          }
        }).catch(e => {
          reject(e);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  public static async downloadPdf(data: any, downloadPath: string, pdfName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const filePath = path.resolve(downloadPath, `${pdfName}.pdf`);
        fs.writeFile(filePath, data, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}

export default PdfFetcher;
