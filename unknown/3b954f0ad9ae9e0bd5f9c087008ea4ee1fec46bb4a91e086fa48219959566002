import React from 'react';
import styles from './TabNavigation.module.scss';

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, setActiveTab }) => {
  const tabs = ['COMPANY', 'USER', 'SHIPMENTS', 'PAYMENTS'];

  return (
    <div className={styles.tabNavigation}>
      {tabs.map(tab => (
        <button
          key={tab}
          className={`${styles.tabButton} ${activeTab === tab ? styles.active : ''}`}
          onClick={() => setActiveTab(tab)}
        >
          {tab}
        </button>
      ))}
    </div>
  );
};

export default TabNavigation; 