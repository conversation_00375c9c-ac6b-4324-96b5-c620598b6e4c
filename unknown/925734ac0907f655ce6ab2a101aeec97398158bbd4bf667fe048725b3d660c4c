import * as yup from 'yup';

export const shipmentsSchema = yup.object({
  deliveryApptRequired: yup.boolean().default(false),
  deliveryAddress: yup.string(),
  deliveryFullAddress: yup.string(),
  deliveryAddressCity: yup.string(),
  deliveryAddressState: yup.string(),
  deliveryAddressZip: yup.string(),
  deliveryContactName: yup.string(),
  deliveryPhoneNumber: yup.string(),
  deliveryEmailAddress: yup.string().email('Invalid email'),
  shippingDocsEmail: yup.string().email('Invalid email'),
  addressNickName: yup.string(),
  dates: yup.array().of(yup.mixed()).optional(),
  resaleCertificateList: yup.array()
  .of(
    yup.object()
      .shape({
        resaleCertFile: yup.mixed(),
        cerificate_url_s3: yup.string().default(null).nullable(),
        state_id: yup.number().nullable()
        .when("cerificate_url_s3", {
          is: (s3Url: any) => s3Url?.trim()?.length > 0, 
          then: (s) => s.required("Required")
        })
        .transform((value)=>{
          if(isNaN(value)){ 
            return null
          }
          return value;
        }),
        expiration_date: yup.string()
        .when("cerificate_url_s3", {
          is: (s3Url: any) => s3Url?.trim()?.length > 0, 
          then: (s) => s.required("Required")
        }),
        uploadCertProgress: yup.boolean().default(false).nullable(),
        status: yup.string().nullable(),
        id: yup.string().nullable(),
        is_deletable: yup.string().nullable(),

      }) 
  ),
});

export type ShipmentsFormData = yup.InferType<typeof shipmentsSchema>; 