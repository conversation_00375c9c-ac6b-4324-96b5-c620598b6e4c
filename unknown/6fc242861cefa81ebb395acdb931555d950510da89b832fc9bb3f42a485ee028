import { Constants } from './Constants';

export class ProductMapping {
    constructor() {
        // Initialize the product mapping
    }

    async searchProductMatches(refProducts, extractedProductsData, weight, weightThreshold, productWeightMapping, lengthThreshold, confidenceRange) {
        // Check if refProducts is an array of objects
        if (!Array.isArray(refProducts) || refProducts.length === 0 || typeof refProducts[0] !== 'object') {
            return [];
        }

        let productIdsDescription = {};
        let productIdsWeight = {}; // for weight mapping of matched products by keywords will be used to filter out products by weight

        // Process each product in the reference products



        // Convert both to lowercase for case-insensitive comparison
        const shapeValue = extractedProductsData?.shape?.toLowerCase();
        const categoryValue = extractedProductsData?.category?.toLowerCase();
        const specValue = extractedProductsData?.spec?.toLowerCase();
        const gradeValue = extractedProductsData?.grade?.toLowerCase();
        const dimensionsValue = extractedProductsData?.dimensions?.toLowerCase();
        const lengthValue = extractedProductsData?.length;

        // Values extracted from extractedProductsData for matching

        // Create an ordered map to maintain the order of keys
        const filteredProductIdsOnFields = {};
        const fieldOrder = [];
        const allDimensions = [];

        const exactMatch = {};
        //only do check for exact match if shape, spec, dimension and length are provided
        const checkExactMatch = shapeValue && specValue && dimensionsValue && lengthValue;

        // Set keys in specific order and track the order
        if (shapeValue) {
            filteredProductIdsOnFields[`${shapeValue}`] = [];
            exactMatch[`${shapeValue}`] = [];
            fieldOrder.push(shapeValue);
        }
        if (categoryValue) {
            filteredProductIdsOnFields[`${categoryValue}`] = [];
            exactMatch[`${categoryValue}`] = [];
            fieldOrder.push(categoryValue);
        }
        if (specValue) {
            filteredProductIdsOnFields[`${specValue}`] = [];
            exactMatch[`${specValue}`] = [];
            fieldOrder.push(specValue);
        }
        if (gradeValue) {
            filteredProductIdsOnFields[`${gradeValue}`] = [];
            exactMatch[`${gradeValue}`] = [];
            fieldOrder.push(gradeValue);
        }
        if (dimensionsValue) {
            filteredProductIdsOnFields[`${dimensionsValue}`] = [];
            exactMatch[`${dimensionsValue}`] = [];
            allDimensions.push(dimensionsValue);
            fieldOrder.push(dimensionsValue);
        }

        for (const product of refProducts) {
            const productId = product.Product_ID;

            // Get all relevant keys from products.json
            // Shape & category keys
            const key2Value = product?.Key2?.toLowerCase() ?? "";  // Shape (Angle, Bar, Beam, etc.)
            const key3Value = product?.Key3?.toLowerCase() ?? "";  // Type (Single, Double, etc.)
            const key4Value = product?.Key4?.toLowerCase() ?? "";  // Category (Equal, Unequal, etc.)
            const key5Value = product?.Key5?.toLowerCase() ?? "";  // Shape symbol (L, C, W, etc.)

            // Dimensions keys
            const key6Value = product?.Key6?.toLowerCase() ?? "";  // Dimension separator (x)
            const key7Value = product?.Key7?.toLowerCase() ?? "";  // Full dimensions (e.g., 3X3X1/4)
            const key8Value = product?.Key8?.toLowerCase() ?? "";  // First dimension with unit (e.g., 3")
            const key9Value = product?.Key9?.toLowerCase() ?? "";  // First dimension decimal (e.g., 3)
            const key10Value = product?.Key10?.toLowerCase() ?? ""; // First dimension precise decimal (e.g., 3.000)
            const key11Value = product?.Key11?.toLowerCase() ?? ""; // Second dimension with unit (e.g., 3")
            const key12Value = product?.Key12?.toLowerCase() ?? ""; // Second dimension decimal (e.g., 3)
            const key13Value = product?.Key13?.toLowerCase() ?? ""; // Third dimension with unit (e.g., 1/4")
            const key14Value = product?.Key14?.toLowerCase() ?? ""; // Third dimension decimal (e.g., 0.25)

            // Spec & grade keys
            const key18Value = product?.Key18?.toLowerCase() ?? ""; // Primary spec (e.g., A36)
            const key19Value = product?.Key19?.toLowerCase() ?? ""; // Secondary spec/grade (e.g., A572-50)
            const key20Value = product?.Key20?.toLowerCase() ?? ""; // Combined spec (e.g., A36/A572-50)

            // UI Description for additional context
            const uiDescription = product?.UI_Description?.toLowerCase() ?? "";

            // SHAPE MATCHING - Check against Key2 (primary shape) and Key4 (can contain shape info)
            if (shapeValue) {
                // More comprehensive shape matching
                let isShapeMatch =
                    key2Value === shapeValue ||
                    key4Value === shapeValue
                
                if(!isShapeMatch){
                    isShapeMatch =
                    key2Value.startsWith(shapeValue) ||
                    key4Value.includes(shapeValue) ||
                    uiDescription.includes(shapeValue);
                }else{
                    exactMatch[`${shapeValue}`].push(productId);
                }
            

                if (isShapeMatch) {
                    filteredProductIdsOnFields[`${shapeValue}`].push(productId);
                    productIdsDescription[`${productId}`] = product.UI_Description;
                    if(productWeightMapping.hasOwnProperty(`${productId}`)){
                        productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                    }
                }
            }

            // CATEGORY MATCHING - Check against Key4 (primary category) and Key3 (can contain category info)
            if (categoryValue) {
                // More comprehensive category matching
                let isCategoryMatch =
                    key4Value === categoryValue ||
                    key3Value === categoryValue ||
                    key11Value === categoryValue ||
                    key5Value === categoryValue

                if(!isCategoryMatch){
                    isCategoryMatch =
                    key4Value.startsWith(categoryValue) ||
                    key3Value.startsWith(categoryValue) ||
                    key11Value.startsWith(categoryValue) ||
                    key5Value.startsWith(categoryValue) ||
                    uiDescription.includes(categoryValue);
                }else{
                    exactMatch[`${categoryValue}`].push(productId);
                }

                if (isCategoryMatch) {
                    filteredProductIdsOnFields[`${categoryValue}`].push(productId);
                    productIdsDescription[`${productId}`] = product.UI_Description;
                    if(productWeightMapping.hasOwnProperty(`${productId}`)){
                        productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                    }
                }
            }

            // SPEC MATCHING - Check against Key18, Key19, and Key20
            if (specValue) {
                // More comprehensive spec matching
                let isSpecMatch =
                    key18Value === specValue ||
                    key19Value === specValue ||
                    key20Value === specValue
                
                if(!isSpecMatch){
                    isSpecMatch =
                    key18Value.startsWith(specValue) ||
                    key19Value.startsWith(specValue) ||
                    key20Value.startsWith(specValue) ||
                    key20Value.includes(specValue) || // For combined specs like A36/A572-50
                    uiDescription.includes(specValue);
                }else{
                    exactMatch[`${specValue}`].push(productId);
                }

                if (isSpecMatch) {
                    filteredProductIdsOnFields[`${specValue}`].push(productId);
                    productIdsDescription[`${productId}`] = product.UI_Description;
                    if(productWeightMapping.hasOwnProperty(`${productId}`)){
                        productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                    }
                }
            }

            // GRADE MATCHING - Check against Key19 (primary grade location) and Key20
            if (gradeValue) {
                // More comprehensive grade matching
                const isGradeMatch =
                    key19Value === gradeValue ||
                    key19Value.includes(`-${gradeValue}`) || // For formats like A572-50
                    key19Value.includes(` ${gradeValue}`) || // For formats like "Grade 50"
                    key20Value.includes(`-${gradeValue}`) || // For combined specs with grade
                    uiDescription.includes(`grade ${gradeValue}`) ||
                    uiDescription.includes(`gr ${gradeValue}`) ||
                    uiDescription.includes(`gr. ${gradeValue}`);

                if (isGradeMatch) {
                    filteredProductIdsOnFields[`${gradeValue}`].push(productId);
                    productIdsDescription[`${productId}`] = product.UI_Description;
                    if(productWeightMapping.hasOwnProperty(`${productId}`)){
                        productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                    }
                }
            }

            // DIMENSIONS MATCHING - Check against multiple dimension keys
            if (dimensionsValue) {
                // First try exact dimension matching
                let isDimensionCorrect = false;

                // Check against (full dimensions)
                let isDimensionMatch =
                    key4Value === dimensionsValue || key5Value === dimensionsValue ||
                    key6Value === dimensionsValue || key7Value === dimensionsValue ||
                    key8Value === dimensionsValue || key9Value === dimensionsValue

                

                if(!isDimensionMatch){
                    isDimensionMatch =
                    key4Value.startsWith(dimensionsValue) || key5Value.startsWith(dimensionsValue) ||
                    key6Value.startsWith(dimensionsValue) || key7Value.startsWith(dimensionsValue) ||
                    key8Value.startsWith(dimensionsValue) || key9Value.startsWith(dimensionsValue)
                }else{
                    exactMatch[`${dimensionsValue}`].push(productId);
                }

                if (isDimensionMatch) {
                    isDimensionCorrect = true;
                    filteredProductIdsOnFields[`${dimensionsValue}`].push(productId);
                    productIdsDescription[`${productId}`] = product.UI_Description;
                    if(productWeightMapping.hasOwnProperty(`${productId}`)){
                        productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                    }
                }

                // If no exact match, try normalized dimension matching (convert fractions to decimals)
                if (!isDimensionCorrect) {
                    // Convert fractions to decimals for comparison
                    let dimensionUpdatedValue = dimensionsValue.replace(/['"]/g, '').replace(/(\d+)\/(\d+)/g, (_, numerator, denominator) => {
                        const value = (Number(numerator) / Number(denominator));
                        if (value) {
                            return value.toFixed(3).replace(/^0/, ''); // e.g., 0.25 -> .250
                        }
                        return _;
                    });

                    isDimensionMatch =
                    this.mapDimensionValue(dimensionUpdatedValue, key4Value) ||
                    this.mapDimensionValue(dimensionUpdatedValue, key5Value) ||
                    this.mapDimensionValue(dimensionUpdatedValue, key6Value) ||
                    this.mapDimensionValue(dimensionUpdatedValue, key7Value) ||
                    this.mapDimensionValue(dimensionUpdatedValue, key8Value) ||
                    this.mapDimensionValue(dimensionUpdatedValue, key9Value);

                    if(isDimensionMatch){
                        console.log('dimensionupdatedValue', dimensionUpdatedValue);
                    }

                    if (isDimensionMatch) {
                        if(fieldOrder.indexOf(dimensionUpdatedValue) === -1){
                            filteredProductIdsOnFields[`${dimensionUpdatedValue}`] = [];
                            fieldOrder.push(dimensionUpdatedValue);
                            allDimensions.push(dimensionUpdatedValue);
                        }
                        filteredProductIdsOnFields[`${dimensionUpdatedValue}`].push(productId);
                        productIdsDescription[`${productId}`] = product.UI_Description;
                        if(productWeightMapping.hasOwnProperty(`${productId}`)){
                            productIdsWeight[`${productId}`] = productWeightMapping[`${productId}`];
                        }
                    }
                }
            }
        }

        // Use the fieldOrder array to maintain the original order
        let searchKeywords = "";
        let filteredProductIds = [];

        // if(allDimensions.length > 1){
        //     let dimensionToUse = allDimensions[0];
        //     for(let index = 0; index < allDimensions.length; index++){
        //         //remove all dimensions from field order array
        //         let fieldIndex = fieldOrder.indexOf();
        //         if (fieldIndex !== -1) {
        //             fieldOrder.splice(fieldIndex, 1);
        //         }
        //         if(index === 0) continue;
        //         if(filteredProductIdsOnFields[allDimensions[index]].length < filteredProductIdsOnFields[dimensionToUse].length && filteredProductIdsOnFields[allDimensions[index]].length !== 0){
        //             dimensionToUse = allDimensions[index];
                   
        //         }
        //     }
        //     fieldOrder.push(dimensionToUse);
        // }

        // Process fields in the order they were added
        for(let i = 0; i < fieldOrder.length; i++){
            const field = fieldOrder[i];
            const productIdsForField = filteredProductIdsOnFields[field];

            // Skip empty arrays
            if(productIdsForField.length === 0){
                continue;
            }

            // Initialize with the first non-empty field
            if(filteredProductIds.length === 0){
                searchKeywords = field;
                filteredProductIds = productIdsForField;
                continue;
            }

            // Filter product IDs that match across fields
            const newFilteredProductIds = filteredProductIds.filter(value => productIdsForField.includes(value));
            if(newFilteredProductIds.length !== 0 && filteredProductIds.length > newFilteredProductIds.length){
                searchKeywords = searchKeywords + `  ${field}`;
                filteredProductIds = newFilteredProductIds;
            }
        }

        filteredProductIds = [...new Set(filteredProductIds)]; // remove duplicate values
        const withoutThresholdProductIdsCount = filteredProductIds.length;
        let filteredProductIdsOnWeight = [];

        // if the filter product id's are more than 1 than filter out product id's by comparing the weight
        let isThresholdApplied = false;
        let exactWeightMatch = false; // exact weight match flag of the filtered product id's
        if(filteredProductIds.length > 1 && weight){ // if the weight is provided then filter the product id's by comparing the weight
            for(const [key, value] of Object.entries(productIdsWeight)){
                if(filteredProductIds.includes(Number(key))){
                    if( Number(value).toFixed(2) === Number(weight).toFixed(2) || Math.abs(Number(value) - Number(weight)) <= 0.01 ){ // if the weight of the product id is exact match or differrene of 0.1 with the weight then set the exact weight match flag to true
                        filteredProductIdsOnWeight.push(Number(key));
                        exactWeightMatch = true;    // setting the exact weight match flag to true
                    }else if(Math.abs(Number(value) - Number(weight)) <= weightThreshold){ // if the weight of the product id is within the weight threshold then add the product id to the filtered product id's array
                        filteredProductIdsOnWeight.push(Number(key));
                    }
                }
            }
            if(filteredProductIdsOnWeight.length > 0){
                searchKeywords = searchKeywords+`(weight ${weight} lb was considered)`
                isThresholdApplied = true;
            }
        }

        filteredProductIds =  filteredProductIdsOnWeight.length > 0 ? filteredProductIdsOnWeight : filteredProductIds;


        let lengthMatch = { // setting the exact length match object
            exact : false,
        };
        if( filteredProductIds.length > 1 && lengthValue){

            const filteredObjects = [];
            const seenKeys = new Set();

            for (const [key,value] of Object.entries(productIdsDescription)) {
                if (filteredProductIds.includes(Number(key)) && !seenKeys.has(key)) {
                    filteredObjects.push({[`${key}`]:value});  // Add object to result
                    seenKeys.add(key); // Mark key as seen to avoid duplicate values
                }
            }

            const lengthMatchedProductIds = await this.searchProductLength(lengthValue, filteredObjects, lengthThreshold, lengthMatch);
            if( lengthMatchedProductIds.length > 0 && lengthMatchedProductIds.length < filteredProductIds.length){
                filteredProductIds = lengthMatchedProductIds;
                searchKeywords = searchKeywords+`(length ${lengthValue} inches was considered)`;
                isThresholdApplied = true;
            }
        }

        exactMatch.filteredProductIds = filteredProductIds;

        if(filteredProductIds.length > 1 && (exactWeightMatch || lengthMatch.exact) && checkExactMatch){
            const arrays = Object.values(exactMatch);
            const [first, ...rest] = arrays;
            const restSets = rest.map(arr => new Set(arr));
            const commonNumbers = first.filter(num => restSets.every(s => s.has(num)));
            if(commonNumbers.length === 1){
                filteredProductIds = commonNumbers;
            }
        }

        // get confidence and status based on the number of filtered product id's from the search keywords
        let confidence = 0;
        let status = Constants.PENDING;;
        if(filteredProductIds.length === 1 && (exactWeightMatch || lengthMatch.exact)){
            confidence = 100;
            status = Constants.APPROVED;
        }else if(withoutThresholdProductIdsCount === 1){
            confidence = 100;
            status = Constants.APPROVED;
        }else if(withoutThresholdProductIdsCount > 1 && isThresholdApplied){
            confidence = 50;
        }else if(withoutThresholdProductIdsCount > 1 && !isThresholdApplied){
            for (const range of confidenceRange) {
                if (withoutThresholdProductIdsCount >= range.min_match_count && (withoutThresholdProductIdsCount <= range.max_match_count || range.max_match_count === null)) {
                    confidence = range.confidence;
                    break;
                }
            };
        }

        return {
            productIds : filteredProductIds,
            searchKeywords : searchKeywords != "" ? searchKeywords.trim() : null,
            confidence : confidence,
            status : status
        }
    }

    mapDimensionValue (dimension, key){
        const dimensionArr = dimension.split("x");
        const keyArr = key.split('x');

        if(dimensionArr.length !== keyArr.length) return false;

        for(let i = 0; i < dimensionArr.length; i++){
            const dimensionPartValue = Number(dimensionArr[i]);
            const keyPartValue = Number(keyArr[i]);

            if(isNaN(dimensionPartValue) || isNaN(keyPartValue) ) return false;

            if(dimensionPartValue !== keyPartValue) return false;
        }
        return true;
    }

    async searchProductLength(length, productIdsDescriptionMapping, lengthThreshold, lengthMatch){
        let lengthMatchedProductIds = [];
        let lengthThresholdMatchedProductIds = [];
        let lengthDivisorMatchedProductIds = [];
        const lengthOfMappedDescription = Object.keys(productIdsDescriptionMapping).length;
        for (const product of productIdsDescriptionMapping) {

            const productId = Number(Object.keys(product)[0]);
            const description = Object.values(product)[0].toString();

            let lengthRangeFormat = description.match(/(\d+)ft to (\d+)ft/) ? description.match(/(\d+)ft to (\d+)ft/) // this regex is for pipe because its description contain lengths in range e.g: 17ft to 24ft
                                    : description.match(/(\d+)ft - (\d+)ft/) ? description.match(/(\d+)ft - (\d+)ft/) // this regex is for pipe because its description contain lengths in range e.g: 17ft - 24ft
                                    : description.match(/(\d+)' to (\d+)'/) ? description.match(/(\d+)' to (\d+)'/) // this regex is for pipe because its description contain lengths in range e.g: 17' to 24'
                                    : description.match(/(\d+)' - (\d+)'/); // this regex is for pipe because its description contain lengths in range e.g: 17' - 24'

            if(lengthRangeFormat){
                const lengthStartRange = Number(lengthRangeFormat[1]) * 12; // convert start range from ft to inces
                const lengthEndRange = Number(lengthRangeFormat[2]) * 12; // convert end range from ft to inces

                if(lengthStartRange <= length && lengthEndRange >= length){ lengthMatchedProductIds.push(Number(productId)); }
            }else {
                const bryzosProductLengthInInches = await this.extractLengthValue(description) ?? 0;
                console.log('bryzosProductLengthInInches', bryzosProductLengthInInches);
                if(Number(length) === Number(bryzosProductLengthInInches)){ // if the length of the bryzos filtred product is exact match with the parameter length then set the exact length match flag to true
                    lengthMatchedProductIds.push(Number(productId));
                    lengthMatch.exact = true; // setting the exact length match flag to true
                }
                if(Math.abs(Number(length) - Number(bryzosProductLengthInInches)) < lengthThreshold){ lengthThresholdMatchedProductIds.push(Number(productId)); } // if length of any bryzos product matched with threshold range
                if( Number(bryzosProductLengthInInches) % Number(length) === 0 ){ lengthDivisorMatchedProductIds.push(Number(productId)); } // if length of any bryzos product is divided by BOM extracted length and reminder is zero
            }
        }

        if(lengthMatchedProductIds.length > 0 && lengthMatchedProductIds.length < lengthOfMappedDescription){
            return lengthMatchedProductIds;
        }

        if(lengthThresholdMatchedProductIds.length > 0 && lengthThresholdMatchedProductIds.length < lengthOfMappedDescription){
            return [ ...new Set( [ ...lengthThresholdMatchedProductIds, ...lengthDivisorMatchedProductIds ] ) ];
        }
        return [];
    }

    async extractLengthValue( productDescription ){
        let lengthInInches = null;

        if ((lengthInInches = productDescription.match(/(\d+)\s*ft/))) { // Check for 'ft' first
            let length = parseInt(lengthInInches[1], 10) * 12; // converting ft into inches
            return length;
        } else if ((lengthInInches = productDescription.match(/(\d+)\s*'/))) { // Check for single quote (')
            let length = parseInt(lengthInInches[1], 10) * 12; // converting ft into inches
            return length;
        } else if ((lengthInInches = productDescription.match(/^(\d+)-\d+$/))) {  // check for format 20-10 here 20 is ft
            let length = parseInt(lengthInInches[1], 10) * 12; // convert ft into inches
            return length
        } else { // Check for double quote (") - find last occurrence
            const lastDoubleQuoteIndex = productDescription.lastIndexOf('"');
            if (lastDoubleQuoteIndex !== -1) {
                const beforeLastQuote = productDescription.substring(0, lastDoubleQuoteIndex);
                lengthInInches = beforeLastQuote.match(/(\d+)\s*$/);
                if (lengthInInches) {
                    return parseInt(lengthInInches[1], 10);
                }
            }
        }
        return null; // If no match is found
    }

}
