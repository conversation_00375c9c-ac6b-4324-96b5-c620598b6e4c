import * as yup from "yup";

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const companySchema = yup.object().shape({
  parentCompanyName: yup.string(),
  companyDBAName: yup.string(),
  companyType: yup.string(),
  companyHQAddress: yup.string(),
  companyHQAddressCity: yup.string(),
  companyHQAddressState: yup.string(),
  companyHQAddressStateCode: yup.string(),
  companyHQAddressZip: yup.string(),
  billingContactName: yup.string(),
  billingContactEmail: yup.string().email('Invalid email format'),
  yourLocationAddress: yup.string(),
  yourLocationAddressCity: yup.string(),
  yourLocationAddressState: yup.string(),
  yourLocationAddressStateCode: yup.string(),
  yourLocationAddressZip: yup.string(),

  sendInvoicesTo: yup.string().trim().required('Send Invoices to is not valid').test('valid-emails', 'Send Invoices to is not valid', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  sendRemittancesTo: yup.string().trim().required('Enter valid email').test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
});

export type CompanyFormData = yup.InferType<typeof companySchema>;