<!DOCTYPE html>
<html>

<head>
  <title>No Internet Access</title>
  <link href="https://fonts.googleapis.com/css?family=Syncopate:300,400,500,600,700,900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      -webkit-user-select: none;
      /* Safari */
      -ms-user-select: none;
      /* IE 10 and IE 11 */
      user-select: none;
      /* Standard syntax */
      font-family: 'Noto Sans', sans-serif;
    }

    html,
    body {
      overflow: hidden;
      border-radius: 16px;
    }

    .app-wrapper {
      width: 100%;
      max-width: 800px;
      margin: 0 auto;
      height: 100%;
      position: relative;
    }

    .wrapper-no-internet {
      padding: 24px;
      position: relative;
    }

    .wrapper-overlay-no-internet {
      position: absolute;
      top: -24px;
      right: -24px;
      bottom: -24px;
      left: -24px;
      background: url(../asset/imgBg3.png)no-repeat center center;
      background-size: cover;
      border-radius: 16px;
    }

    .no-internet-header {
      height: 120px;
      width: 100%;
      display: flex;
      justify-content: left;
      align-items: center;
      padding-left: 32px;
      background-color: #0f0f14;
      background-image: url(Header-No-Internet.svg);
      background-size: cover;
      background-position: center;
      position: relative;
      overflow: hidden;
      border-radius: 16px 16px 0px 0px;
    }

    .bgEllips {
      position: absolute;
      top: 0px;
      left: 0px;
      opacity: 0.56;
      -webkit-filter: blur(100px);
      filter: blur(100px);
      background-image: conic-gradient(from 0.25turn, #fff, rgba(255, 255, 255, 0) 0.19turn, #fff 0.4turn, rgba(255, 255, 255, 0) 0.55turn, #fff 0.76turn, rgba(255, 255, 255, 0) 0.97turn, #fff), linear-gradient(147deg, #e7ecef 75%, rgba(231, 236, 239, 0) 200%), linear-gradient(to bottom, #9786ff, #9786ff);
      width: 231.9px;
      height: 54.6px;
    }

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-image: linear-gradient(to bottom, #0f0f14, #0f0f14), linear-gradient(179deg, #fff -12%, #1a1b21 28%);
      height: 630px;
      width: 100%;
      padding-top: 104px;
      border-radius: 0px 0px 16px 16px;
    }

    .no_internet_img {
      width: 104px;
      height: 104px;
      margin: 0 0 24px 0;
      object-fit: contain;
    }

    .try_again_btn {
      gap: 8px;
      padding: 14px 12px;
      border-radius: 10px;
      background-color: #fff;
      border: none;
      outline: none;
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.72px;
      text-align: center;
      color: #0f0f14;
      cursor: pointer;
      margin-top: 40px;
      width: 220px;
      height: 50px;
    }

    .content {
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
      margin-top: 8px;
    }

    .header {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: -0.96px;
      text-align: left;
      color: #fff;
      /* -webkit-app-region: drag; */
    }

    .no-internet-btn {
      position: absolute;
      top: 1.3vw;
      right: 3vw;
      z-index: 2;
    }

    .no-internet-btn button {
      background-color: transparent;
      border: 0px;
      box-shadow: none;
      display: inline-block;
      transition: all 0.1s;
      cursor: pointer;
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button img {
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }

    .drag-panel {
      -webkit-app-region: drag;
      width: 100%;
      height: 5vw;
      display: inline-flex;
      position: absolute;
      left: 1px;
      top: 1px;
    }

    .btn {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      margin-right: 5px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .btn:before,
    .btn:after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 4px;
      opacity: 0;
      transition: all 300ms ease-in-out;
    }

    .btn:hover::before,
    .btn:hover::after {
      top: 49%;
      opacity: 1;
      left: 49%;
    }

    .close-btn {
      background: #FF5D5B;
      border: 2px solid #CF544D;
    }

    .close-btn:before,
    .close-btn:after {
      width: 1.5px;
      height: 100%;
      background: #000;
    }

    .close-btn:before {
      transform: translate(-50%, -50%) rotate(45deg);
    }

    .close-btn:after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }

    .min-btn {
      background: #FFBB39;
      border: 2px solid #CFA64E;
    }

    .min-btn:before {
      width: 70%;
      height: 1.5px;
      background: #000;
    }

    .closeAndMinimizeBtn {
      display: flex;
      flex-direction: row-reverse;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: -17px;
      right: -21px;
      z-index: 999999;
    }

    .macCloseAndMinimizeBtn {
      left: -20px;
      right: unset;
      flex-direction: row;
    }
  </style>
</head>

<body class="no-internet-body">
  <div class="wrapper-no-internet">
    <div class="wrapper-overlay-no-internet"></div>
    <div class="app-wrapper">
      <span class="drag-panel"></span>
      <div class="closeAndMinimizeBtn">
        <div class="btn close-btn" id="close-button"></div>
        <div class="btn min-btn" id="minimize-app-button"></div>
      </div>
      <div class="no-internet-header">
        <img src="new-bryzos-logo-with-text.svg" alt="Logo" width="223px" height="47px" />
      </div>
      <div class="container">
        <img class="no_internet_img" src="./No_Internet_Connection.svg" alt="No Internet Connection" />
        <p class="header">NO INTERNET CONNECTION</p>
        <p class="content">Please check your internet connection</p>
        <button class="try_again_btn" id="try-again-button">TRY AGAIN</button>
      </div>

    </div>
    
  </div>
  <script>
    let isMacDevice = false;

    // Check if electron is available and get system info
    if (window.electron) {
      const os = window.electron.sendSync({ channel: 'systemVersion' });
      console.log("os", os);
      isMacDevice = os.includes('Mac');

      // Add Mac-specific class to closeAndMinimizeBtn
      if (isMacDevice) {
        const closeAndMinimizeBtn = document.querySelector('.closeAndMinimizeBtn');
        if (closeAndMinimizeBtn) {
          closeAndMinimizeBtn.classList.add('macCloseAndMinimizeBtn');
        }
      }
    }

    const tryAgainButton = document.getElementById('try-again-button');
    tryAgainButton.addEventListener('click', () => {
      // Notify the main process to reload the window
      console.log("window.electron", window.electron);
      if (window.electron)
        window.electron.send({ channel: 'reload-window' });
    });

    const minimizeAppButton = document.getElementById('minimize-app-button');
    minimizeAppButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowMinimize' });
    });

    const closeButton = document.getElementById('close-button');
    closeButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowClose' });
    });

    // Add class to body based on OS
    if (isMacDevice) {
      document.body.classList.add('isMacDevice');
    }
  </script>
</body>

</html>