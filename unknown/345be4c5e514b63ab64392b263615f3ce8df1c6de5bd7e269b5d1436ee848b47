{"branch": "QA", "webpage": "https://d32lsi7nsygihc.cloudfront.net", "updateUrl": "https://extended-widget-ui-hazel.vercel.app", "internetCheckURL": "https://qabryzoswidget.com/widget-service/reference-data/homepage", "referenceProductPricingDomain": "https://bryzoswidget.com/widget-service", "pusherNotification": {"pusher": {"key": "25083145e623b199a9ad", "cluster": "ap2"}, "channels": ["extended-pricing-widget-notification-staging", "private-channel-staging-", "extended-pricing-widget-buyer-notification-staging", "extended-pricing-widget-seller-notification-staging"], "authUrl": "https://qabryzoswidget.com/notification-service", "channelEvents": {"privateEvents": ["NOTIFICATION_BUYER_CHECKOUT", "NOTIFICATION_SELLER_CHECKOUT", "NOTIFICATION_BNPL_APPROVAL_STATUS", "NOTIFICATION_BUYER_INVOICE_READY", "NOTIFICATION_SELLER_FUNDING_INITIATED", "NOTIFICATION_BUYER_SALES_TAX_REMINDER", "NOTIFICATION_SELLER_ORDER_PREVIEW", "NOTIFICATION_SELLER_ORDER_CLAIM", "NOTIFICATION_BUYER_ORDER_CANCEL", "NOTIFICATION_BUYER_ORDER_LINE_CANCEL", "NOTIFICATION_SELLER_ORDER_CANCEL", "NOTIFICATION_SELLER_ORDER_LINE_CANCEL", "NOTIFICATION_BUYER_NEVER_PURCHASED", "NOTIFICATION_SELLER_NEVER_CLAIMED", "CUSTOM_NOTIFICATION"], "publicEvents": ["NOTIFICATION_PUBLIC", "CUSTOM_NOTIFICATION"], "buyerEvents": ["CUSTOM_NOTIFICATION"], "sellerEvents": ["NOTIFICATION_SELLER_ORDER_PREVIEW", "NOTIFICATION_SELLER_ORDER_CLAIM", "NOTIFICATION_SELLER_NEW_BUYER_ADDED", "CUSTOM_NOTIFICATION"]}}, "commonAppEventsofPusher": {"privateEvents": {"userForceLogout": "USER_FORCE_LOGOUT", "userDiscountPriceChange": "USER_DISCOUNT_PRICE_CHANGE", "customNotification": "CUSTOM_NOTIFICATION", "userUiUploadLog": "USER_UI_UPLOAD_LOG"}, "publicEvents": {"referenceProductChanges": ["NOTIFICATION_PRICE_CHANGES", "NOTIFICATION_PRODUCT_CHANGES", "NOTIFICATION_PRICE_PRODUCT_CHANGES"], "customNotification": "CUSTOM_NOTIFICATION"}, "buyerEvents": {"customNotification": "CUSTOM_NOTIFICATION"}, "sellerEvents": {"customNotification": "CUSTOM_NOTIFICATION"}}}