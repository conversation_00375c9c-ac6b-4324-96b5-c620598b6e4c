import { useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library"
import SearchHeader from "../../SearchHeader"
import styles from "./SavedBom.module.scss"
import CreatePo from "../createPo";
import { useEffect } from "react";
import { useState } from "react";
const SavedBom = () => {
    const {createPoDataFromSavedBom, bomProductMappingDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom} = useCreatePoStore();
    const [showNoRecords, setShowNoRecords] = useState(true);
    
    useEffect(() => {
        return () => {
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
        }
    },[])

    useEffect(() => {
        if(createPoDataFromSavedBom || bomProductMappingDataFromSavedBom){
            setShowNoRecords(false);
        }else{
            setShowNoRecords(true);
        }
    },[createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])
    
    if(showNoRecords){
        return (
            <>
                <SearchHeader />
                <div className={styles.savedBomContainer}>
                    <div className={styles.noOrderDetailsContainer}>
                        <p>NO ORDER DETAILS TO DISPLAY</p>
                        <p>SELECT SAVED PO FROM THE LEFT</p>
                    </div>
                </div>
            </>
                
        )
    }
    return (
         <CreatePo />
    )
}

export default SavedBom;
