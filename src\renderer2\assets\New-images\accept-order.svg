<svg xmlns="http://www.w3.org/2000/svg" width="290" height="58" viewBox="0 0 290 58" fill="none">
    <g>
        <path d="M4 18C4 12.477 8.477 8 14 8h262c5.523 0 10 4.477 10 10v30c0 5.523-4.477 10-10 10H14C8.477 58 4 53.523 4 48V18z" fill="#222329"  shape-rendering="crispEdges"/>
        <path d="M67.195 35.715h-6.732L59.329 38H55.69l6.32-12.076h3.639L71.968 38h-3.639l-1.134-2.285zm-5.563-2.356h4.412l-2.197-4.447-2.215 4.447zm24.724 3.815c-.422.176-.846.334-1.274.474-.428.141-.87.261-1.327.36-.457.106-.94.185-1.45.238-.505.053-1.046.08-1.627.08-1.23 0-2.364-.133-3.4-.396-1.032-.264-1.923-.66-2.673-1.187a5.656 5.656 0 0 1-1.74-1.986c-.416-.797-.624-1.726-.624-2.786s.208-1.987.624-2.778a5.557 5.557 0 0 1 1.74-1.986c.75-.533 1.64-.932 2.672-1.195 1.037-.264 2.171-.396 3.401-.396.58 0 1.123.027 1.627.08.51.052.993.131 1.45.237.457.1.9.22 1.327.36.428.14.852.299 1.274.475v2.935a22.09 22.09 0 0 0-1.054-.536 8.696 8.696 0 0 0-1.222-.483 9.803 9.803 0 0 0-1.468-.352 10.469 10.469 0 0 0-1.802-.14c-1.025 0-1.878.105-2.557.316-.674.21-1.213.492-1.618.844a2.894 2.894 0 0 0-.852 1.212c-.164.452-.246.92-.246 1.407 0 .322.035.641.105.958.07.31.188.606.352.887.164.276.378.53.641.765.264.234.592.437.985.606.392.17.85.305 1.371.405.527.093 1.134.14 1.82.14.667 0 1.268-.04 1.801-.123a10.618 10.618 0 0 0 1.468-.342c.445-.141.852-.3 1.222-.475.369-.182.72-.366 1.054-.554v2.936zm15.628 0c-.422.176-.847.334-1.275.474-.427.141-.87.261-1.327.36-.457.106-.94.185-1.45.238-.504.053-1.046.08-1.626.08-1.23 0-2.364-.133-3.401-.396-1.031-.264-1.922-.66-2.672-1.187a5.655 5.655 0 0 1-1.74-1.986c-.416-.797-.624-1.726-.624-2.786s.208-1.987.624-2.778a5.556 5.556 0 0 1 1.74-1.986c.75-.533 1.64-.932 2.672-1.195 1.037-.264 2.17-.396 3.401-.396.58 0 1.122.027 1.626.08.51.052.993.131 1.45.237.457.1.9.22 1.327.36.428.14.853.299 1.275.475v2.935c-.334-.181-.686-.36-1.055-.536a8.683 8.683 0 0 0-1.222-.483 9.805 9.805 0 0 0-1.467-.352 10.469 10.469 0 0 0-1.802-.14c-1.025 0-1.878.105-2.558.316-.673.21-1.212.492-1.617.844a2.895 2.895 0 0 0-.852 1.212c-.164.452-.246.92-.246 1.407 0 .322.035.641.105.958.07.31.188.606.352.887.164.276.377.53.641.765.264.234.592.437.984.606.393.17.85.305 1.372.405.527.093 1.133.14 1.819.14.668 0 1.269-.04 1.802-.123a10.62 10.62 0 0 0 1.468-.342 9.46 9.46 0 0 0 1.221-.475c.369-.182.721-.366 1.055-.554v2.936zm2.321.826V25.924h12.225v2.575h-8.876v1.995h8.419v2.575h-8.419v2.356h9V38h-12.349zm28.354-7.954c0 .603-.088 1.16-.264 1.67-.175.504-.46.94-.852 1.31-.387.363-.891.647-1.512.852-.621.205-1.377.307-2.267.307h-5.414V38h-3.261V25.924h8.675c.89 0 1.646.102 2.267.307.621.2 1.125.484 1.512.853.392.363.677.797.852 1.3.176.505.264 1.058.264 1.662zm-3.296.053c0-.282-.041-.522-.123-.721a1.049 1.049 0 0 0-.387-.501 1.843 1.843 0 0 0-.711-.281 5.132 5.132 0 0 0-1.082-.097h-4.71v3.111h4.71c.428 0 .789-.023 1.082-.07.292-.053.53-.138.711-.255.182-.123.311-.281.387-.474.082-.194.123-.431.123-.712zm12.051-1.547V38h-3.261v-9.448h-5.124v-2.628h13.518v2.628h-5.133zm28.249 3.419c0 1.037-.19 1.954-.571 2.75a5.368 5.368 0 0 1-1.635 1.987c-.709.533-1.57.938-2.584 1.213-1.008.27-2.141.404-3.401.404-1.26 0-2.4-.135-3.419-.404-1.014-.276-1.878-.68-2.593-1.213a5.403 5.403 0 0 1-1.652-1.986c-.387-.797-.58-1.714-.58-2.751s.193-1.951.58-2.742a5.384 5.384 0 0 1 1.652-1.996c.715-.533 1.579-.934 2.593-1.204 1.019-.275 2.159-.413 3.419-.413 1.26 0 2.393.138 3.401.413 1.014.27 1.875.671 2.584 1.204a5.349 5.349 0 0 1 1.635 1.995c.381.791.571 1.706.571 2.743zm-3.296 0c0-.451-.073-.9-.219-1.345a2.866 2.866 0 0 0-.765-1.213c-.363-.357-.861-.647-1.494-.87-.633-.223-1.439-.334-2.417-.334a8.43 8.43 0 0 0-1.732.158c-.498.1-.928.24-1.292.422a3.48 3.48 0 0 0-.914.642c-.246.24-.442.5-.589.782a3.297 3.297 0 0 0-.316.87 4.573 4.573 0 0 0 0 1.793c.064.305.17.598.316.879.147.275.343.533.589.773.246.24.551.452.914.633a5.68 5.68 0 0 0 1.292.422c.498.1 1.076.15 1.732.15.978 0 1.784-.112 2.417-.335.633-.222 1.131-.512 1.494-.87.369-.357.624-.758.765-1.204.146-.451.219-.902.219-1.353zM175.175 38h-3.261V25.924h8.174c.879 0 1.623.09 2.232.272.615.182 1.113.44 1.494.774.387.334.665.738.835 1.213.176.468.264.993.264 1.573 0 .51-.073.955-.22 1.336-.14.38-.334.709-.58.984a3.42 3.42 0 0 1-.844.686 5.73 5.73 0 0 1-1.019.465L186.205 38h-3.815l-3.656-4.447h-3.559V38zm6.442-8.262c0-.234-.032-.433-.097-.597a.773.773 0 0 0-.316-.396 1.573 1.573 0 0 0-.615-.228 5.332 5.332 0 0 0-.95-.07h-4.464v2.583h4.464c.381 0 .698-.023.95-.07.257-.047.463-.12.615-.22a.817.817 0 0 0 .316-.404c.065-.164.097-.363.097-.598zm20.998 2.215c0 .774-.085 1.462-.255 2.066-.17.597-.41 1.119-.721 1.564a4.525 4.525 0 0 1-1.107 1.134 5.65 5.65 0 0 1-1.433.747 8.852 8.852 0 0 1-1.705.413c-.609.082-1.245.123-1.907.123h-7.664V25.924h7.646c.662 0 1.298.044 1.907.132.61.082 1.178.216 1.706.404a5.761 5.761 0 0 1 1.45.747c.433.305.803.683 1.107 1.134.311.445.551.967.721 1.564.17.598.255 1.28.255 2.048zm-3.287 0c0-.58-.077-1.084-.229-1.512a2.301 2.301 0 0 0-.721-1.054c-.328-.282-.758-.49-1.292-.624-.533-.14-1.183-.211-1.951-.211h-4.051v6.82h4.051c.768 0 1.418-.067 1.951-.202.534-.14.964-.352 1.292-.633.334-.287.575-.644.721-1.072.152-.428.229-.932.229-1.512zM204.874 38V25.924H217.1v2.575h-8.877v1.995h8.42v2.575h-8.42v2.356h9V38h-12.349zm18.045 0h-3.261V25.924h8.174c.879 0 1.623.09 2.232.272.616.182 1.114.44 1.495.774.386.334.665.738.835 1.213.175.468.263.993.263 1.573 0 .51-.073.955-.22 1.336-.14.38-.334.709-.58.984-.24.27-.521.498-.843.686a5.739 5.739 0 0 1-1.02.465L233.949 38h-3.814l-3.657-4.447h-3.559V38zm6.442-8.262c0-.234-.032-.433-.096-.597a.775.775 0 0 0-.317-.396 1.567 1.567 0 0 0-.615-.228 5.32 5.32 0 0 0-.949-.07h-4.465v2.583h4.465c.381 0 .697-.023.949-.07.258-.047.463-.12.615-.22a.819.819 0 0 0 .317-.404c.064-.164.096-.363.096-.598z" fill="url(#jgkq4y7j2c)"/>
    </g>
    <defs>
        <linearGradient x1="293.099" y1="114.466" x2="10.277" y2="61.273" gradientUnits="userSpaceOnUse">
            <stop stop-color="#222329"/>
            <stop offset="1" stop-color="#222329"/>
        </linearGradient>
        <linearGradient id="jgkq4y7j2c" x1="239.006" y1="70.974" x2="64.863" y2="25.78" gradientUnits="userSpaceOnUse">
            <stop stop-color="rgba(255, 255, 255, 0.4)"/>
            <stop offset="1" stop-color="rgba(255, 255, 255, 0.4)"/>
        </linearGradient>
        <filter id="8gidb7pw5a" x="0" y="0" width="290" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1811_2943"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_1811_2943" result="shape"/>
        </filter>
    </defs>
</svg>