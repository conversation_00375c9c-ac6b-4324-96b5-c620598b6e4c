<svg width="294" height="722" viewBox="0 0 294 722" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_744_2019)">
<rect x="1" y="1" width="292" height="720" rx="13" fill="#23242A" fill-opacity="0.2"/>
</g>
<rect x="0.5" y="0.5" width="293" height="721" rx="13.5" stroke="url(#paint0_linear_744_2019)" stroke-opacity="0.33"/>
<defs>
<filter id="filter0_i_744_2019" x="0" y="0" width="298" height="726" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_744_2019"/>
</filter>
<linearGradient id="paint0_linear_744_2019" x1="229.597" y1="870.565" x2="-307.15" y2="527.273" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B20" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
