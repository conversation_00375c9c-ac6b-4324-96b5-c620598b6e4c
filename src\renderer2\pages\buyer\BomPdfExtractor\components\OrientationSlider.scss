.orientation-slider-container {
  display: flex;
  width: 100%;
  font-family: 'Syncopate', sans-serif;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.orientation-label {
  font-size: 10px;
  font-weight: normal;
  letter-spacing: 0.4px;
  text-transform: uppercase;
  color: #fff;
  padding-right: 10px;
}

.slider-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.slider-track {
  position: relative;
  flex: 1;
  height: 20px;
  background-color: #2a2a2a;
  border-radius: 2px;
  border: 1px solid #444;
  width: 185px
}

.slider-input {
  width: 100%;
  height: 100%;
  background: transparent;
  outline: none;
  border: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 18px;
    background-color: #fff;
    border: 1px solid #666;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    z-index: 2;

    &:hover {
      background-color: #f0f0f0;
    }
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 18px;
    background-color: #fff;
    border: 1px solid #666;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    z-index: 2;

    &:hover {
      background-color: #f0f0f0;
    }
  }
}

.marks-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.mark {
  position: absolute;
  top: 0;
  height: 100%;
  transform: translateX(-50%);
}

.mark-line {
  width: 1px;
  height: 100%;
  background-color: #666;
  margin: 0 auto;
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  min-width: 60px;
  justify-content: flex-end;
}

.value-number {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  font-family: 'Syncopate', sans-serif;
}

.value-unit {
  font-size: 10px;
  font-weight: normal;
  color: #999;
  font-family: 'Syncopate', sans-serif;
}