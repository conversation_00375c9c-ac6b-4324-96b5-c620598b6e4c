<svg width="126" height="189" viewBox="0 0 126 189" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_851_1434)">
<g clip-path="url(#clip0_851_1434)">
<rect width="126" height="189" rx="8" fill="#131314"/>
<g filter="url(#filter1_f_851_1434)">
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="#9786FF"/>
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="url(#paint0_linear_851_1434)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_851_1434_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0314019 0.0371387 -0.0294314 0.0518875 29.8316 -32.3273)"><foreignObject x="-940.952" y="-940.952" width="1881.9" height="1881.9"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:62.803733825683594,&#34;m01&#34;:-58.862773895263672,&#34;m02&#34;:27.861078262329102,&#34;m10&#34;:74.277313232421875,&#34;m11&#34;:103.77507019042969,&#34;m12&#34;:-121.35349273681641},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.8" filter="url(#filter2_f_851_1434)">
<circle cx="97.704" cy="211.704" r="14.7041" fill="#9786FF"/>
<circle cx="97.704" cy="211.704" r="14.7041" fill="url(#paint2_linear_851_1434)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_851_1434_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.014704 0.0192571 -0.0137814 0.0269046 97.7041 212.898)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97.704" cy="211.704" r="14.7041" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:29.408096313476562,&#34;m01&#34;:-27.562726974487305,&#34;m02&#34;:96.781364440917969,&#34;m10&#34;:38.514160156250,&#34;m11&#34;:53.809295654296875,&#34;m12&#34;:166.73655700683594},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<path d="M37.1211 12.9688C37.1211 13.4844 37.0645 13.9434 36.9512 14.3457C36.8379 14.7441 36.6777 15.0918 36.4707 15.3887C36.2676 15.6855 36.0215 15.9375 35.7324 16.1445C35.4473 16.3516 35.1289 16.5176 34.7773 16.6426C34.4258 16.7676 34.0469 16.8594 33.6406 16.918C33.2344 16.9727 32.8105 17 32.3691 17H27.2598V8.94922H32.3574C32.7988 8.94922 33.2227 8.97852 33.6289 9.03711C34.0352 9.0918 34.4141 9.18164 34.7656 9.30664C35.1211 9.43164 35.4434 9.59766 35.7324 9.80469C36.0215 10.0078 36.2676 10.2598 36.4707 10.5605C36.6777 10.8574 36.8379 11.2051 36.9512 11.6035C37.0645 12.002 37.1211 12.457 37.1211 12.9688ZM34.9297 12.9688C34.9297 12.582 34.8789 12.2461 34.7773 11.9609C34.6797 11.6758 34.5195 11.4414 34.2969 11.2578C34.0781 11.0703 33.791 10.9316 33.4355 10.8418C33.0801 10.748 32.6465 10.7012 32.1348 10.7012H29.4336V15.248H32.1348C32.6465 15.248 33.0801 15.2031 33.4355 15.1133C33.791 15.0195 34.0781 14.8789 34.2969 14.6914C34.5195 14.5 34.6797 14.2617 34.7773 13.9766C34.8789 13.6914 34.9297 13.3555 34.9297 12.9688ZM39.1074 17V8.94922H47.2578V10.666H41.3398V11.9961H46.9531V13.7129H41.3398V15.2832H47.3398V17H39.1074ZM49.4434 17V8.94922H51.6172V15.2363H57.4941V17H49.4434ZM59.2402 17V8.94922H67.3906V10.666H61.4727V11.9961H67.0859V13.7129H61.4727V15.2832H67.4727V17H59.2402ZM74.0879 10.7012V17H71.9141V10.7012H68.498V8.94922H77.5098V10.7012H74.0879ZM78.9512 17V8.94922H87.1016V10.666H81.1836V11.9961H86.7969V13.7129H81.1836V15.2832H87.1836V17H78.9512ZM99.1484 12.9688C99.1484 13.4844 99.0918 13.9434 98.9785 14.3457C98.8652 14.7441 98.7051 15.0918 98.498 15.3887C98.2949 15.6855 98.0488 15.9375 97.7598 16.1445C97.4746 16.3516 97.1562 16.5176 96.8047 16.6426C96.4531 16.7676 96.0742 16.8594 95.668 16.918C95.2617 16.9727 94.8379 17 94.3965 17H89.2871V8.94922H94.3848C94.8262 8.94922 95.25 8.97852 95.6562 9.03711C96.0625 9.0918 96.4414 9.18164 96.793 9.30664C97.1484 9.43164 97.4707 9.59766 97.7598 9.80469C98.0488 10.0078 98.2949 10.2598 98.498 10.5605C98.7051 10.8574 98.8652 11.2051 98.9785 11.6035C99.0918 12.002 99.1484 12.457 99.1484 12.9688ZM96.957 12.9688C96.957 12.582 96.9062 12.2461 96.8047 11.9609C96.707 11.6758 96.5469 11.4414 96.3242 11.2578C96.1055 11.0703 95.8184 10.9316 95.4629 10.8418C95.1074 10.748 94.6738 10.7012 94.1621 10.7012H91.4609V15.248H94.1621C94.6738 15.248 95.1074 15.2031 95.4629 15.1133C95.8184 15.0195 96.1055 14.8789 96.3242 14.6914C96.5469 14.5 96.707 14.2617 96.8047 13.9766C96.9062 13.6914 96.957 13.3555 96.957 12.9688Z" fill="url(#paint4_radial_851_1434)"/>
</g>
<rect x="0.183801" y="0.183801" width="125.632" height="188.632" rx="7.8162" stroke="url(#paint5_linear_851_1434)" stroke-opacity="0.4" stroke-width="0.367601"/>
</g>
<defs>
<filter id="filter0_ii_851_1434" x="-2" y="-1" width="132" height="194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1434"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_851_1434" result="effect2_innerShadow_851_1434"/>
</filter>
<filter id="filter1_f_851_1434" x="-38.3304" y="-99.7484" width="136.325" height="130.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_851_1434"/>
</filter>
<clipPath id="paint1_angular_851_1434_clip_path"><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578"/></clipPath><filter id="filter2_f_851_1434" x="60.9439" y="174.944" width="73.5184" height="73.5204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_851_1434"/>
</filter>
<clipPath id="paint3_angular_851_1434_clip_path"><circle cx="97.704" cy="211.704" r="14.7041"/></clipPath><linearGradient id="paint0_linear_851_1434" x1="29.8316" y1="-45.8584" x2="70.3543" y2="-21.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_851_1434" x1="97.704" y1="205.882" x2="117.665" y2="216.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_851_1434" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(28.4944 4.5) rotate(21.4236) scale(188.906 19.5813)">
<stop stop-color="#FC3030"/>
<stop offset="1" stop-color="#3F4046"/>
</radialGradient>
<linearGradient id="paint5_linear_851_1434" x1="140.415" y1="99.8338" x2="20.3616" y2="25.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1C21"/>
</linearGradient>
<clipPath id="clip0_851_1434">
<rect width="126" height="189" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
