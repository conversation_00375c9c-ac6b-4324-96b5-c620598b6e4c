import RBush from 'rbush';
import { TextractResult, TextractBlock } from './types/PdfExtractorTypes';

interface RBushItem {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
  block: TextractBlock;
}

/**
 * Creates a spatial index for Textract blocks using RBush
 * @param {TextractResult} textractData - The Textract analysis results
 * @returns {RBush<RBushItem>} - RBush spatial index
 */
export function createTextractRBush(textractData: TextractResult): RBush<RBushItem> {
  const tree = new RBush<RBushItem>();

  if (!textractData || !textractData.Blocks || !Array.isArray(textractData.Blocks)) {
    return tree;
  }

  // Insert all blocks into the spatial index
  textractData.Blocks.forEach(block => {
    if (block.BoundingBox) {
      const item: RBushItem = {
        minX: block.BoundingBox.Left,
        minY: block.BoundingBox.Top,
        maxX: block.BoundingBox.Left + block.BoundingBox.Width,
        maxY: block.BoundingBox.Top + block.BoundingBox.Height,
        block
      };
      tree.insert(item);
    }
  });

  return tree;
}

/**
 * Finds blocks that intersect with a given bounding box
 * @param {RBush<RBushItem>} tree - The RBush spatial index
 * @param {Object} bbox - The bounding box to search for
 * @param {number} bbox.left - Left coordinate
 * @param {number} bbox.top - Top coordinate
 * @param {number} bbox.width - Width of the box
 * @param {number} bbox.height - Height of the box
 * @returns {TextractBlock[]} - Array of intersecting blocks
 */
export function findIntersectingBlocks(
  tree: RBush<RBushItem>,
  bbox: { left: number; top: number; width: number; height: number }
): TextractBlock[] {
  const searchBox = {
    minX: bbox.left,
    minY: bbox.top,
    maxX: bbox.left + bbox.width,
    maxY: bbox.top + bbox.height
  };

  const results = tree.search(searchBox);
  return results.map(item => item.block);
} 