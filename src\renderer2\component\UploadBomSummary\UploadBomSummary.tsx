import React, { useEffect, useState } from 'react';
import styles from './UploadBomSummary.module.scss';
import clsx from 'clsx';
import { uploadBomConst, useGlobalStore, useCreatePoStore, commomKeys } from '@bryzos/giss-ui-library';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { routes, bomLineStatusCountObjDefault } from 'src/renderer2/common';
import { useNavigate } from 'react-router-dom';
import usePostBomReviewComplete from 'src/renderer2/hooks/usePostBomReviewComplete';
import useSaveBomHeaderDetails from 'src/renderer2/hooks/useSaveBomHeaderDetails';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { ReactComponent as DropdownIcon } from '../../assets/New-images/Dropdpown_Up_Arrow.svg';
import { useLeftPanelStore } from '../LeftPanel/LeftPanelStore';
import useDialogStore from '../DialogPopup/DialogStore';
import { useBomPdfExtractorStore } from 'src/renderer2/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';

// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface UploadBomSummaryProps {}

const UploadBomSummary: React.FC<UploadBomSummaryProps> = () => {
  const createPOStore: any = useCreatePoStore();
  const { props, bomLineStatusCountObj, setBOMLineStatusCountObj , setScrollToBomLine } = useRightWindowStore();
  const { getValues, disableReviewCompleteButton, bomProductMappingSocketData, watch } = props;
  const [selectOpen, setSelectOpen] = useState(false);

  const { mutateAsync: postBomReviewComplete } = usePostBomReviewComplete();
  const { mutate: mutateSaveBomHeaderDetails } = useSaveBomHeaderDetails();
  const navigate = useNavigate();

  const saveBomHeaderDetails = () => {
    try {
        const payload = {
            "data": {
                "bom_upload_id": bomProductMappingSocketData?.id ?? '',
                "bom_name": getValues('internal_po_number') ?? '',
                "bom_type": getValues('order_type') ?? '',
                "delivery_date": getValues('delivery_date') ?? '',
                "shipping_details": {
                    "line1": getValues('shipping_details.line1') ?? '',
                    "line2": getValues('shipping_details.line2')?.trim() || null,
                    "city": getValues('shipping_details.city') ?? '',
                    "zip": getValues('shipping_details.zip') ?? '',
                    "state_id": getValues('shipping_details.state_id') ?? ''
                }
            }
        }
        if(payload?.data?.bom_upload_id && payload.data.bom_name && payload.data.bom_type && payload.data.delivery_date && payload.data.shipping_details.line1 && payload.data.shipping_details.city && payload.data.shipping_details.zip && payload.data.shipping_details.state_id){
          mutateSaveBomHeaderDetails(payload)
        }
    } catch (error) {
        console.error(error)
    }
  }
  const cartItems = watch('cart_items') || [];
  const totalLines = cartItems.length;
  
  // Calculate counts for each status
  const readyLines = cartItems.length - bomLineStatusCountObj[uploadBomConst.lineItemStatus.pending];
  const approvedLines = bomLineStatusCountObj[uploadBomConst.lineItemStatus.approved];
  const pendingLines = bomLineStatusCountObj[uploadBomConst.lineItemStatus.pending];
  const skippedLines = bomLineStatusCountObj[uploadBomConst.lineItemStatus.skipped];
  const deletedLines = bomLineStatusCountObj[uploadBomConst.lineItemStatus.deleted];
  
  // Calculate percentages for each status with minimum display value
  const getBarWidth = (count: number) => {
    if (totalLines === 0) return '3%'; // Minimum bar width when there are no lines
    const percentage = Math.round((count / totalLines) * 100);
    return percentage > 0 ? `${percentage}%` : '3%'; // Minimum 3% width for visibility
  };

  // Filter cartItems based on the selected filter
  const getFilteredCartItems = () => {
    switch(createPOStore.bomSummaryViewFilter) {
      case 'red':
        return cartItems.filter((item: any) => item?.lineStatus === uploadBomConst.lineItemStatus.pending);
      case 'green':
        return cartItems.filter((item: any) => item?.lineStatus !== uploadBomConst.lineItemStatus.pending);
      case 'all':
      default:
        return cartItems;
    }
  };

  // Get filtered items for display
  const filteredCartItems = getFilteredCartItems();
  
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  const {resetBomPdfExtractorStore} = useBomPdfExtractorStore();
  const {setUploadBomInitialData} = useCreatePoStore();

  const handleReviewComplete = async () => {
    const formattedCartItems = getValues('cart_items').filter((item: any) => item?.lineStatus !== 'DELETED');
    createPOStore.setCreatePoData({
      ...getValues(),
      cart_items: formattedCartItems ,
      bom_id: bomProductMappingSocketData?.id
    });
    createPOStore.setIsCreatePoDirty(true);
    const payload = {
      "data": {
        "bom_upload_id": bomProductMappingSocketData?.id
      }
    };
    const response = await postBomReviewComplete(payload);
    console.log(response);
    if(response?.error_message){
      showCommonDialog(null, response?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
      return;
    }
    resetBomPdfExtractorStore();
    setUploadBomInitialData(null);
    saveBomHeaderDetails()
    setBOMLineStatusCountObj({...bomLineStatusCountObjDefault});
    navigate(routes.createPoPage);
  };

  const handleViewFilterChange = (event: any) => {
    createPOStore.setBomSummaryViewFilter(event.target.value);
    setSelectOpen(false);
  };

  const toggleSelect = () => {
    setSelectOpen(!selectOpen);
  };

  const handleBomLineClick = (item: any) => {
    setScrollToBomLine(item?.bom_line_id)
  }
  
  return (
    <div className={clsx(styles.container, 'orderSummaryContainer')}>
      <div className={styles.fadeIn}>
        <div className={styles.slideUp} style={{ animationDelay: '100ms' }}>
          <div className={styles.titleContainer}>
            <p className={styles.mainTitle}>BOM Summary</p>
            <p className={styles.subtitle}>Must be 100% Ready to Continue</p> 
          </div>
          
          <div className={styles.summaryContainer}>
            <div className={styles.headerTabs}>
              <div className={styles.tabHeader}>
                <div className={styles.tab}>AERIAL</div>
                <div className={clsx(styles.tab, styles.activeTab)}>INFORMATION</div>
              </div>
              
              <div className={styles.tabContent}>
                <div className={styles.sectionsLayout}>
                  {/* Left side: Select and Status Items */}
                  <div className={styles.leftColumn}>
                    <div className={styles.filterHeader} onClick={toggleSelect}>
                      <span>View {createPOStore.bomSummaryViewFilter === 'all' ? 'All' : createPOStore.bomSummaryViewFilter === 'red' ? 'Red' : 'Green'}</span>
                      <span className={styles.dropdownArrow}><DropdownIcon/></span>
                    </div>
                      <Select
                        value={createPOStore.bomSummaryViewFilter}
                        onChange={handleViewFilterChange}
                        open={selectOpen}
                        onOpen={() => setSelectOpen(true)}
                        onClose={() => setSelectOpen(false)}
                        className={'selectDropdownBOM'}
                        MenuProps={
                          {
                            classes:{
                              paper:styles.dropDownBG
                            },
                          }
                        }
                      >
                        <MenuItem value="red" className={styles.menuItem}>
                          <div className={styles.menuItemContent}>
                            <span>Red</span>
                          </div>
                        </MenuItem>
                        <MenuItem value="green" className={styles.menuItem}>
                          <div className={styles.menuItemContent}>
                            <span>Green</span>
                          </div>
                        </MenuItem>
                        <MenuItem value="all" className={styles.menuItem}>
                          <div className={styles.menuItemContent}>
                            <span>All</span>
                          </div>
                        </MenuItem>
                      </Select>
                    
                    <div className={styles.verticalNumbers}>
                      {filteredCartItems.map((item: any, index: number) => (
                        <div className={styles.verticalNumbersContent} onClick={() => handleBomLineClick(item)}>
                        <div key={index} className={styles.numberItem}>
                          <span>{cartItems.indexOf(item) + 1}</span>
                          {item.lineStatus === uploadBomConst.lineItemStatus.pending ? 
                            <div className={styles.redDot}></div>
                            :
                            <div className={styles.greenDot}></div>
                          }
                        </div>
                         <div className={styles.leftColumnBox1}></div>
                         <div className={styles.leftColumnBox2}></div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Right side: Information Panel */}
                  <div className={styles.rightColumn}>
                    <div className={styles.infoPanel}>
                      <div className={styles.infoTop}>
                        <div className={styles.infoStats}>
                          <div className={styles.statItem}>
                            <p className={styles.statLabel}>Total Lines</p>
                            <p className={styles.statValue}>{totalLines}</p>
                          </div>
                          <div className={styles.statItem}>
                            <p className={styles.statLabel}>Ready <span className={styles.greenDot}></span></p>
                            <p className={styles.statValue}>{readyLines}</p>
                          </div>
                          <div className={styles.statItem}>
                            <p className={styles.statLabel}>Not Ready <span className={styles.redDot}></span></p>
                            <p className={styles.statValue}>{pendingLines}</p>
                          </div>
                        </div>
                        
                        <div className={styles.readyPercentage}>
                          <p className={styles.percentageValue}>{totalLines > 0 ? Math.round((readyLines / totalLines) * 100) : 0}<span>%</span></p>
                          <p className={styles.percentageLabel}>Ready</p>
                        </div>
                      </div>
                      
                      {/* Progress Bars */}
                      <div className={styles.progressBarsSection}>
                        {/* Approved Progress Bar */}
                        <div className={styles.statusProgressBar}>
                          <div className={styles.progressBarLabel}>
                            <span className={styles.progressLabel}>APPROVED</span>
                            <span className={styles.progressCount}>{approvedLines}</span>
                          </div>
                          <div className={styles.progressBarContainer}>
                            <div className={styles.progressBar}>
                              <div 
                                className={clsx(styles.progressBarFill, styles.approvedFill)} 
                                style={{ width: getBarWidth(approvedLines) }}
                              />
                            </div>
                          </div>
                        </div>
                        
                        {/* Pending Progress Bar */}
                        <div className={styles.statusProgressBar}>
                          <div className={styles.progressBarLabel}>
                            <span className={styles.progressLabel}>PENDING</span>
                            <span className={styles.progressCount}>{pendingLines}</span>
                          </div>
                          <div className={styles.progressBarContainer}>
                            <div className={styles.progressBar}>
                              <div 
                                className={clsx(styles.progressBarFill, styles.pendingFill)} 
                                style={{ width: getBarWidth(pendingLines) }}
                              />
                            </div>
                          </div>
                        </div>
                        
                        {/* Skipped Progress Bar */}
                        <div className={styles.statusProgressBar}>
                          <div className={styles.progressBarLabel}>
                            <span className={styles.progressLabel}>SKIPPED</span>
                            <span className={styles.progressCount}>{skippedLines}</span>
                          </div>
                          <div className={styles.progressBarContainer}>
                            <div className={styles.progressBar}>
                              <div 
                                className={clsx(styles.progressBarFill, styles.skippedFill)} 
                                style={{ width: getBarWidth(skippedLines) }}
                              />
                            </div>
                          </div>
                        </div>
                        
                        {/* Deleted Progress Bar */}
                        <div className={styles.statusProgressBar}>
                          <div className={styles.progressBarLabel}>
                            <span className={styles.progressLabel}>DELETED</span>
                            <span className={styles.progressCount}>{deletedLines}</span>
                          </div>
                          <div className={styles.progressBarContainer}>
                            <div className={styles.progressBar}>
                              <div 
                                className={clsx(styles.progressBarFill, styles.deletedFill)} 
                                style={{ width: getBarWidth(deletedLines) }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className={styles.instructionText}>
            <p>
              Please review all uploaded lines for accuracy in
              product and quantity. Once reviewed and<br/>
              corrected as needed, click Review Complete to
              generate the pricing for this bill of material.
            </p>
          </div>
          
          {/* Review Complete Button */}
          <button
            id="review-complete-btn"
            className={clsx(styles.orderButton, styles.slideUp)}
            style={{ animationDelay: '500ms' }}
            disabled={disableReviewCompleteButton}
            onClick={handleReviewComplete}
          >
            REVIEW COMPLETE
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadBomSummary;
