<svg width="321" height="1000" viewBox="0 0 321 1000" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Side Menu - Shorter">
<g id="Side Menu">
<g id="Container">
<g clip-path="url(#clip0_865_3595)">
<rect y="1" width="321" height="999" rx="21" fill="#191A20"/>
<g id="Shorter - Bottom - BG" filter="url(#filter0_d_865_3595)">
<path d="M0 233H321V979.051C321 990.621 311.621 1000 300.051 1000H20.9487C9.37907 1000 0 990.621 0 979.051V233Z" fill="url(#paint0_linear_865_3595)"/>
<path d="M0.498779 233.499H320.501V979.051C320.501 990.345 311.345 999.501 300.051 999.501H20.9487C9.65454 999.501 0.498779 990.345 0.498779 979.051V233.499Z" stroke="url(#paint1_linear_865_3595)" stroke-opacity="0.22" stroke-width="0.997558"/>
</g>
</g>
<rect x="0.5" y="1.5" width="320" height="998" rx="20.5" stroke="url(#paint2_radial_865_3595)" stroke-opacity="0.4"/>
</g>
<g id="Header - Shorter - BG" filter="url(#filter1_d_865_3595)">
<g clip-path="url(#clip1_865_3595)">
<path d="M0 16C0 7.16344 7.16344 0 16 0H305C313.837 0 321 7.16345 321 16V120H0V16Z" fill="#0F0F14"/>
<g id="Ellipse 6594" opacity="0.8" filter="url(#filter2_f_865_3595)">
<circle cx="257" cy="180" r="40" fill="#9786FF"/>
<circle cx="257" cy="180" r="40" fill="url(#paint3_linear_865_3595)" fill-opacity="0.8"/>
<g clip-path="url(#paint4_angular_865_3595_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.04 0.0523858 -0.03749 0.0731898 257 183.249)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="257" cy="180" r="40" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:79.999992370605469,&#34;m01&#34;:-74.979957580566406,&#34;m02&#34;:254.4899902343750,&#34;m10&#34;:104.77156829833984,&#34;m11&#34;:146.37953186035156,&#34;m12&#34;:57.673175811767578},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g id="Ellipse 6595" opacity="0.56">
<g id="Ellipse 6593" filter="url(#filter3_f_865_3595)">
<ellipse cx="63" cy="-113.5" rx="116" ry="129.5" fill="#9786FF"/>
<ellipse cx="63" cy="-113.5" rx="116" ry="129.5" fill="url(#paint5_linear_865_3595)" fill-opacity="0.8"/>
<g clip-path="url(#paint6_angular_865_3595_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.116 0.169599 -0.108721 0.236952 63 -102.982)"><foreignObject x="-937.498" y="-937.498" width="1875" height="1875"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="63" cy="-113.5" rx="116" ry="129.5" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:231.99996948242188,&#34;m01&#34;:-217.44187927246094,&#34;m02&#34;:55.720954895019531,&#34;m10&#34;:339.19796752929688,&#34;m11&#34;:473.90371704101562,&#34;m12&#34;:-509.53308105468750},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
</g>
<path d="M16 0.5H305C313.56 0.5 320.5 7.43959 320.5 16V119.5H0.5V16C0.5 7.43959 7.43959 0.5 16 0.5Z" stroke="url(#paint7_radial_865_3595)" stroke-opacity="0.4"/>
<path d="M16 0.5H305C313.56 0.5 320.5 7.43959 320.5 16V119.5H0.5V16C0.5 7.43959 7.43959 0.5 16 0.5Z" stroke="url(#paint8_linear_865_3595)" stroke-opacity="0.27"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_865_3595" x="-4.08999" y="212.949" width="329.18" height="787.051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="10.9731" operator="erode" in="SourceAlpha" result="effect1_dropShadow_865_3595"/>
<feOffset dy="-15.9609"/>
<feGaussianBlur stdDeviation="7.53156"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_865_3595"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_865_3595" result="shape"/>
</filter>
<filter id="filter1_d_865_3595" x="-1.4" y="0" width="323.8" height="126.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="erode" in="SourceAlpha" result="effect1_dropShadow_865_3595"/>
<feOffset dy="5.5"/>
<feGaussianBlur stdDeviation="2.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_865_3595"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_865_3595" result="shape"/>
</filter>
<filter id="filter2_f_865_3595" x="157" y="80" width="200" height="200" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_865_3595"/>
</filter>
<clipPath id="paint4_angular_865_3595_clip_path"><circle cx="257" cy="180" r="40"/></clipPath><filter id="filter3_f_865_3595" x="-153" y="-343" width="432" height="459" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_865_3595"/>
</filter>
<clipPath id="paint6_angular_865_3595_clip_path"><ellipse cx="63" cy="-113.5" rx="116" ry="129.5"/></clipPath><linearGradient id="paint0_linear_865_3595" x1="385.73" y1="2165.19" x2="-1245.53" y2="715.371" gradientUnits="userSpaceOnUse">
<stop stop-color="#2B2D33"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint1_linear_865_3595" x1="156.964" y1="133.366" x2="167.884" y2="443.782" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B21"/>
</linearGradient>
<radialGradient id="paint2_radial_865_3595" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(99.2425 -15.8436) rotate(97.8729) scale(242.159 82.884)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_865_3595" x1="257" y1="164.162" x2="311.3" y2="193.809" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_865_3595" x1="63" y1="-164.774" x2="227.955" y2="-84.1002" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint7_radial_865_3595" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(99.2425 -2.02326) rotate(139.02) scale(43.9374 54.8722)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint8_linear_865_3595" x1="-14.445" y1="340.165" x2="190.819" y2="187.566" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<clipPath id="clip0_865_3595">
<rect y="1" width="321" height="999" rx="21" fill="white"/>
</clipPath>
<clipPath id="clip1_865_3595">
<path d="M0 16C0 7.16344 7.16344 0 16 0H305C313.837 0 321 7.16345 321 16V120H0V16Z" fill="white"/>
</clipPath>
</defs>
</svg>
