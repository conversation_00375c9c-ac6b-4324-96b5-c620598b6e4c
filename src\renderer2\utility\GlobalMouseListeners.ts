import { useEffect, useState } from 'react';
import { useHoverVideo } from '../hooks/useHoverVideo';
import {hoverVideoConfig} from '../hoverVideoCommon';
import { hoverVideoAttribute } from '../common';

const GlobalMouseListeners = () => {
  const { handleHoverVideoMouseEnter, handleHoverVideoMouseLeave } = useHoverVideo();

  useEffect(() => {
    const handleMouseOver = (e: MouseEvent) => {
      const target = (e.target as HTMLElement).closest('[data-hover-video-id]') as HTMLElement | null;
      if (!target) return;
  
      const related = e.relatedTarget as HTMLElement | null;
      if (related && target.contains(related)) return; // Ignore internal movements
  
      const videoId = target.getAttribute('data-hover-video-id');
      if (videoId && hoverVideoConfig[videoId]) {
        handleHoverVideoMouseEnter(videoId);
      }
    };
  
    const handleMouseOut = (e: MouseEvent) => {
      const target = (e.target as HTMLElement).closest('[data-hover-video-id]') as HTMLElement | null;
      if (!target) return;
  
      const related = e.relatedTarget as HTMLElement | null;
      if (related && target.contains(related)) return; // Ignore internal movements
  
      const videoId = target.getAttribute('data-hover-video-id');
      if (videoId && hoverVideoConfig[videoId]) {
        handleHoverVideoMouseLeave();
      }
    };
  
    document.addEventListener('mouseover', handleMouseOver);
    document.addEventListener('mouseout', handleMouseOut);
  
    return () => {
      document.removeEventListener('mouseover', handleMouseOver);
      document.removeEventListener('mouseout', handleMouseOut);
    };
  }, []);
  

  return null;
};

export default GlobalMouseListeners;
