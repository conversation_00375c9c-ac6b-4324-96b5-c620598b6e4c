.videoPlayerRightWindowContainer{
    width: 322px;
    height: 456px;
    margin-top: 24px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    border-style: solid;
    border-width: 1px;
    border-image-source: radial-gradient(circle at 31% 10%, #fff, rgba(255, 255, 255, 0) 22%);
    border-image-slice: 1;
    background-image: linear-gradient(162deg, #0f0f14 -26%, #393e47 226%);
    background-origin: border-box;
    background-clip: content-box, border-box;

    .videoPlayerSection {
        width: 100%;
        height: 275px;
        position: relative;
        z-index: 0;
    } 

    .videoThumbnailContainer{
        position: relative;
    }
    .playBtnContainer{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
    }
    .informationContainer{
        width: 322px;
        height: 175px;
        padding: 24px 22px 50px 20px;
        box-shadow: 0 -16px 15.1px -11px #000;
        border-style: solid;
        border-width: 1px;
        border-image-source: linear-gradient(180deg, #fff -112%, #1a1b21 28%);
        border-image-slice: 1;
        background-image: linear-gradient(162deg, #0f0f14 -26%, #393e47 226%);
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        .title{
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.2;
            letter-spacing: 1.12px;
            text-align: left;
            color: rgba(255, 255, 255, 0.8);
        }
        .description{
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.15;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.8);
        }
    }
    .closeButton{
        position: absolute;
        top: 9px;
        right: 13px;
        width: 20px;
        height: 20px;
        color: #fff;
        cursor: pointer;
        z-index: 100;
    }
}

