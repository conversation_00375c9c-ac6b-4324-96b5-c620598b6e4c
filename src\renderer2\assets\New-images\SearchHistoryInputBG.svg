<svg width="288" height="38" viewBox="0 0 288 38" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="rgba(0, 0, 0, 0.5)" />
      <stop offset="100%" stop-color="rgba(0, 0, 0, 0.3)" />
    </linearGradient>
  </defs>
  
  <!-- Main input background -->
  <rect width="288" height="38" rx="12" fill="url(#inputGradient)" />
  
  <!-- Subtle border glow effect -->
  <rect x="0.5" y="0.5" width="287" height="37" rx="11.5" stroke="rgba(255, 255, 255, 0.05)" />
  
  <!-- Inner shadow effect -->
  <rect x="1" y="1" width="286" height="36" rx="11" fill="transparent" stroke="rgba(0, 0, 0, 0.3)" stroke-opacity="0.3" />
</svg> 