<svg width="800" height="120" viewBox="0 0 800 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1995_2420)">
<path d="M0 16C0 7.16344 7.16344 0 16 0H784C792.837 0 800 7.16344 800 16V120H0V16Z" fill="#0F0F14"/>
<g opacity="0.56">
<g filter="url(#filter0_f_1995_2420)">
<ellipse cx="115.898" cy="-74.8516" rx="116" ry="129.5" fill="#9786FF"/>
<ellipse cx="115.898" cy="-74.8516" rx="116" ry="129.5" fill="url(#paint0_linear_1995_2420)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_1995_2420_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.116 0.169599 -0.108721 0.236952 115.898 -64.3338)"><foreignObject x="-937.498" y="-937.498" width="1875" height="1875"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="115.898" cy="-74.8516" rx="116" ry="129.5" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:231.99996948242188,&#34;m01&#34;:-217.44187927246094,&#34;m02&#34;:108.61939239501953,&#34;m10&#34;:339.19796752929688,&#34;m11&#34;:473.90371704101562,&#34;m12&#34;:-470.88464355468750},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_1995_2420" x="-100.102" y="-304.352" width="432" height="459" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1995_2420"/>
</filter>
<clipPath id="paint1_angular_1995_2420_clip_path"><ellipse cx="115.898" cy="-74.8516" rx="116" ry="129.5"/></clipPath><linearGradient id="paint0_linear_1995_2420" x1="115.898" y1="-126.126" x2="280.854" y2="-45.4518" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1995_2420">
<path d="M0 16C0 7.16344 7.16344 0 16 0H784C792.837 0 800 7.16344 800 16V120H0V16Z" fill="white"/>
</clipPath>
</defs>
</svg>
