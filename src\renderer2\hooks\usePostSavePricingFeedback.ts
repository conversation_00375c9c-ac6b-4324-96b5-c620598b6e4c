import { commom<PERSON><PERSON><PERSON>, useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import useDialogStore from "../component/DialogPopup/DialogStore";

const usePostSavePricingFeedback = () => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation(async (data: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/save-pricing-feedback`,
        data
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          showCommonDialog(null, response.data.data.error_message, null, resetDialogStore, [
            { name: commomKeys.errorBtnTitle, action: resetDialogStore }
          ]);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostSavePricingFeedback;
