.priceSearchHistory {
  width: 320px;
  background: #191a20;
  background-size: 100% 100%;
  border-radius: 15px;
  color: white;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
  height: 100%;
}

.headerMain {
  width: 100%;
  height: 154px;
  margin: 0 0 16px;
  padding: 20px 20px 18px 20px;
  box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
  background: url(../../assets/New-images/SearchHistoryheaderBg.svg) no-repeat;
  position: relative;
  overflow: hidden;

  .header {
    font-family: Syncopate;
    font-size: 20px;
    font-weight: bold;
    line-height: 1.2;
    letter-spacing: 0.8px;
    text-align: center;
    color: #fff;
    margin-bottom: 28px;
  }

}

.searchBox {
  width: 100%;
  height: 41px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 16px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.04);
  transition: background-color 0.1s ease-in-out;
  z-index: 2;

  &:focus-within {
    background: url('../../assets/New-images/SearchInputActive.svg') no-repeat bottom;
    background-size: cover;
  }

  input {
    background-color: transparent;
    border: none;
    width: 100%;
    height: 100%;
    padding: 6px 12px 6px 0;
    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    letter-spacing: 0.6px;
    color: #1fbbfe;
    transition: all 0.1s ease-in-out;

    &::placeholder {
      color: #616575;
    }

    &:focus {
      outline: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
  }

  svg {
    margin-left: 18px;
  }
}

.historyList {
  height:calc(100% - 170px);
  padding: 0px 10px 10px 10px;

  .historyItemList {
    max-height: 100%;
    overflow-y: auto;
    padding-right: 5px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &::-webkit-scrollbar {
      width: 5px;
      height: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.32);
      border-radius: 50px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.historyItem {
  padding: 10px;
  position: relative;
  transition: background 0.1s;

  &:hover {
    background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
    background-size: cover;
    cursor: pointer;
    border-radius: 10px;

    .historyItemTitle {
      color: #0f0f14;
    }

    .historyItemDetails {
      color: #0f0f14;
    }

    .editIcon {
      opacity: 1;
      visibility: visible;
    }

    .dustbinIcon {
      opacity: 1;
      visibility: visible;
    }
  }
}

.historyItemTitle {
  font-family: Inter;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
  margin-bottom: 1px;
  min-height: 20px;
  display: flex;
  align-items: center;
  
  .historyItemTitleText {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.editIcon {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.1);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.dustbinIcon {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  position: absolute;
  right: 10px;
  top: 45%;
  transform: translateY(-50%);
  z-index: 10;


  svg {
    width: 18px;
    height: 18px;
    color: #ff4757;
  }
}

.deleteConfirmationIcons {
      position: absolute;
    right: 28px;
    top: 45%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 15;
    padding: 3px 10px;
}

.confirmDeleteIcon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(34, 197, 94, 0.9);
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
    background: rgba(34, 197, 94, 1);
  }

  svg {
    width: 12px;
    height: 12px;
    color: white;
  }
}

.cancelDeleteIcon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.9);
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
    background: rgba(239, 68, 68, 1);
  }

  svg {
    width: 12px;
    height: 12px;
    color: white;
  }
}

.activeItem {
  background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
  background-size: cover;
}

.historyItemDetails {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}

.detailsRow {
  display: flex;
  justify-content: space-between;
  &.detailsRowGrid{
    display: grid;
    justify-content: start;
    column-gap: 14px;
    grid-template-columns: 100px 100px;
  }
}

// Add animation for hover effect
@keyframes pulse {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

.noResults {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  padding: 180px 0;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
}

.editMode {
  background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
  background-size: cover;
  padding: 10px;
  .historyItemDetails{
    color: #0f0f14;
    cursor: default;
  }
}

.editTitleRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editTitleInput {
  flex: 1;
  background: transparent;
  border: none;
  font-family: Inter;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #0f0f14;
  height: 20px;

  &:focus {
    outline: none;
  }
}

.editButtons {
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.editButtonsShifted {
  transform: translateX(-60px);
}

.editButton {
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: left;
  color: #393e47;
  margin-right: 12px;
}

.backButton {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: color 0.2s ease;
  padding-left: 20px;

  svg {
    margin-right: 4px;
    width: 12px;
    height: 12px;
  }

  &:hover {
    color: white;
  }
}

.historyItemExpanded {
  padding: 0px 20px 0px 20px;
}

.expandedHistoryItemTitle {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.priceList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  height:calc(100% - 283px); 
  padding: 12px 4px 12px 12px;
  box-shadow: 0 -16px 15.1px -11px #000;
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(to bottom, #fff -66%, #1a1b21 15%);
  border-image-slice: 1;
  max-height: 100%;

  .priceItemList {
    height: 100%;
    max-height:100%;
    // overflow-y: auto;
    padding-right: 4px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    
  }
}

.priceItem {
  padding: 20px 12px 0;
  background-color: rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  border-radius: 0px 0px 20px 20px;
  height: 100%;
  overflow:auto;
  &::-webkit-scrollbar {
      width: 5px;
      height: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.32);
      border-radius: 50px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }

  .priceItemContent{
    margin-bottom: 24px;
  }
}

.priceItemTitle {
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-right: 12px;
  text-transform: uppercase;
 
}

.productDescription1{
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  margin-bottom: 2px;
  text-transform: uppercase;
}

.miscDesc{
  letter-spacing: 0;
}

.priceItemDetails {
  font-size: 12px;
}

.priceValue {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
  gap: 2px;
  min-width: 80px;
  margin-top: auto;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;

  .price {
    .textStyle1 {
      font-family: Inter;
      font-size: 10px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.4px;
      text-align: right;
      color: rgba(255, 255, 255, 0.56);
      vertical-align: super;
      margin-right: 2px;
    }

    span {
      font-family: Inter;
      font-size: 18px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: right;
      color: #fff;
    }
  }

  .priceUnit {
    font-family: Inter;
    font-size: 10px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.4px;
    text-align: right;
    color: rgba(255, 255, 255, 0.56);

    span {
      color: rgba(255, 255, 255, 0.92);
    }
  }
}

.priceRow {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: flex-start;
}

.loading{
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emailTooltip.emailTooltip {
  background-color: rgba(255, 255, 255, 0.7);
  padding: 6px 12px;
  border-radius: 3px;
  max-width: 250px;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  font-family: Noto Sans;
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
  color: #000;
}