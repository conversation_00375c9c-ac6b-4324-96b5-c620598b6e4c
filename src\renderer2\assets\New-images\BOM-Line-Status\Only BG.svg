<svg width="126" height="189" viewBox="0 0 126 189" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_851_1757)">
<g clip-path="url(#clip0_851_1757)">
<rect width="126" height="189" rx="8" fill="#131314"/>
<g filter="url(#filter1_f_851_1757)">
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="#9786FF"/>
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="url(#paint0_linear_851_1757)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_851_1757_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0314019 0.0371387 -0.0294314 0.0518875 29.8316 -32.3273)"><foreignObject x="-940.952" y="-940.952" width="1881.9" height="1881.9"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:62.803733825683594,&#34;m01&#34;:-58.862773895263672,&#34;m02&#34;:27.861078262329102,&#34;m10&#34;:74.277313232421875,&#34;m11&#34;:103.77507019042969,&#34;m12&#34;:-121.35349273681641},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.8" filter="url(#filter2_f_851_1757)">
<circle cx="97.704" cy="211.704" r="14.7041" fill="#9786FF"/>
<circle cx="97.704" cy="211.704" r="14.7041" fill="url(#paint2_linear_851_1757)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_851_1757_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.014704 0.0192571 -0.0137814 0.0269046 97.7041 212.898)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97.704" cy="211.704" r="14.7041" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:29.408096313476562,&#34;m01&#34;:-27.562726974487305,&#34;m02&#34;:96.781364440917969,&#34;m10&#34;:38.514160156250,&#34;m11&#34;:53.809295654296875,&#34;m12&#34;:166.73655700683594},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.183801" y="0.183801" width="125.632" height="188.632" rx="7.8162" stroke="url(#paint4_linear_851_1757)" stroke-opacity="0.4" stroke-width="0.367601"/>
</g>
<defs>
<filter id="filter0_ii_851_1757" x="-2" y="-1" width="132" height="194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1757"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_851_1757" result="effect2_innerShadow_851_1757"/>
</filter>
<filter id="filter1_f_851_1757" x="-38.3304" y="-99.7484" width="136.325" height="130.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_851_1757"/>
</filter>
<clipPath id="paint1_angular_851_1757_clip_path"><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578"/></clipPath><filter id="filter2_f_851_1757" x="60.9439" y="174.944" width="73.5184" height="73.5204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_851_1757"/>
</filter>
<clipPath id="paint3_angular_851_1757_clip_path"><circle cx="97.704" cy="211.704" r="14.7041"/></clipPath><linearGradient id="paint0_linear_851_1757" x1="29.8316" y1="-45.8584" x2="70.3543" y2="-21.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_851_1757" x1="97.704" y1="205.882" x2="117.665" y2="216.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_851_1757" x1="140.415" y1="99.8338" x2="20.3616" y2="25.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1C21"/>
</linearGradient>
<clipPath id="clip0_851_1757">
<rect width="126" height="189" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
