import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useGetBomSavedData = () => {
  return useMutation(async (params?: Record<string, any>) => {
    try {
      const API_URL = import.meta.env.VITE_API_SERVICE + "/user/bom";
      const response = await axios.get(API_URL, { params });

      if (response.data?.error_message) {
        throw new Error(response.data.error_message);
      }

      return response.data;
    } catch (error: any) {
      throw new Error(error?.message || "Failed to fetch BOM data");
    }
  });
};

export default useGetBomSavedData;
