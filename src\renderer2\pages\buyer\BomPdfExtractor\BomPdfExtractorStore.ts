import { create } from 'zustand';

const defaultStore   = {
    extractText: false,
    autoSelectColumns: false,
    gridOpacity: 0.7,
    currentBoxType:'description',
    pdfFile:null,
    snapToGrid:true,
    showMagnifyingGlass:true,
    overlapPercent:40,
    pdfFileName:'',
    textractRBushInitialized :false,
    doResetAll:false,
    doUndoBox:false,
    doExportCSV:false,
    doClearAll:false,
    zoomPercentage:100,
    pdfjs:null,
    allBoxes:[],
    sourceCanvasRefs:[],
    boxDrawing:0,
    s3Url:'',
    doNext:false,
    isImageBasedPdf: false,
    bomUploadID: null,
    geometryData: null,
    domesticOnly:false,
    pageRotations:0,
    scrollViewPort: null,
    updatedScrollViewPort: null,
    customBoxTypes:[],
    doResetBomPdfExtractorStore: {value:true},
    showBackToBomUploadButton: false,
    textractRBush:null,
    doAutoScroll: false,
    pdfUrl:'',
    hasBoxes:false,
    canvasRotation:0,
    undoStack: [],
    redoStack: [],
    columnDef:[],
    rowData:[]
}
export const RESIZE_BOX = 'resize_box'
export const ZOOM = 'zoom'
export const ADD_BOX = 'add_box'
export const DELETE_BOX = 'delete_box'
export const ROTATE = 'rotate'


export const useBomPdfExtractorStore = create((set, get) => ({
  ...defaultStore,
  bomData: null,

  setColumnDef: (v) => set(state => ({ columnDef: typeof v === 'function' ? v(state.columnDef) : v })),
  setRowData: (v) => set(state => ({ rowData: typeof v === 'function' ? v(state.rowData) : v })),
  setExtractText: (v) => set(state => ({ extractText: typeof v === 'function' ? v(state.extractText) : v })),
  setAutoSelectColumns: (v) => set(state => ({ autoSelectColumns: typeof v === 'function' ? v(state.autoSelectColumns) : v })),
  setGridOpacity: (v) => set(state => ({ gridOpacity: typeof v === 'function' ? v(state.gridOpacity) : v })),
  setCurrentBoxType: (v) => set(state => ({ currentBoxType: typeof v === 'function' ? v(state.currentBoxType) : v })),
  setPdfFile: (v) => set(state => ({ pdfFile: typeof v === 'function' ? v(state.pdfFile) : v })),
  setSnapToGrid: (v) => set(state => ({ snapToGrid: typeof v === 'function' ? v(state.snapToGrid) : v })),
  setShowMagnifyingGlass: (v) => set(state => ({ showMagnifyingGlass: typeof v === 'function' ? v(state.showMagnifyingGlass) : v })),
  setOverlapPercent: (v) => set(state => ({ overlapPercent: typeof v === 'function' ? v(state.overlapPercent) : v })),
  setPdfFileName: (v) => set(state => ({ pdfFileName: typeof v === 'function' ? v(state.pdfFileName) : v })),
  setTextractRBushInitialized: (v) => set(state => ({ textractRBushInitialized: typeof v === 'function' ? v(state.textractRBushInitialized) : v })),
  setDoResetAll: (v) => set(state => ({ doResetAll: typeof v === 'function' ? v(state.doResetAll) : v })),
  setDoExportCSV: (v) => set(state => ({ doExportCSV: typeof v === 'function' ? v(state.doExportCSV) : v })),
  setDoClearAll: (v) => set(state => ({ doClearAll: typeof v === 'function' ? v(state.doClearAll) : v })),
  setZoomPercentage: (v) => set(state => ({ zoomPercentage: typeof v === 'function' ? v(state.zoomPercentage) : v })),
  setPdfjs: (v) => set(state => ({ pdfjs: typeof v === 'function' ? v(state.pdfjs) : v })),
  setAllBoxes: (v) => set(state => ({ allBoxes: typeof v === 'function' ? v(state.allBoxes) : v })),
  setSourceCanvasRefs: (v) => set(state => ({ sourceCanvasRefs: typeof v === 'function' ? v(state.sourceCanvasRefs) : v })),
  setBoxDrawing: (v) => set(state => ({ boxDrawing: typeof v === 'function' ? v(state.boxDrawing) : v })),
  setS3Url: (v) => set(state => ({ s3Url: typeof v === 'function' ? v(state.s3Url) : v })),
  setDoNext: (v) => set(state => ({ doNext: typeof v === 'function' ? v(state.doNext) : v })),
  setBomData: (v) => set(state => ({ bomData: typeof v === 'function' ? v(state.bomData) : v })),
  setIsImageBasedPdf: (v) => set(state => ({ isImageBasedPdf: typeof v === 'function' ? v(state.isImageBasedPdf) : v })),
  setBomUploadID: (v) => set(state => ({ bomUploadID: typeof v === 'function' ? v(state.bomUploadID) : v })),
  setGeometryData: (v) => set(state => ({ geometryData: typeof v === 'function' ? v(state.geometryData) : v })),
  setDomesticOnly: (v) => set(state => ({ domesticOnly: typeof v === 'function' ? v(state.domesticOnly) : v })),
  setPageRotations: (v) => set(state => ({ pageRotations: typeof v === 'function' ? v(state.pageRotations) : v })),
  setScrollViewPort: (v) => set(state => ({ scrollViewPort: typeof v === 'function' ? v(state.scrollViewPort) : v })),
  setCustomBoxTypes: (v) => set(state => ({ customBoxTypes: typeof v === 'function' ? v(state.customBoxTypes) : v })),
  setUpdatedScrollViewPort: (v) => set(state => ({ updatedScrollViewPort: typeof v === 'function' ? v(state.updatedScrollViewPort) : v })),
  setShowBackToBomUploadButton: (v) => set(state => ({ showBackToBomUploadButton: typeof v === 'function' ? v(state.showBackToBomUploadButton) : v })),
  setTextractRBush: (v) => set(state => ({ textractRBush: typeof v === 'function' ? v(state.textractRBush) : v })),
  setdoAutoScroll: (v) => set(state => ({ doAutoScroll: typeof v === 'function' ? v(state.doAutoScroll) : v })),
  setPdfUrl: (v) => set(state => ({ pdfUrl: typeof v === 'function' ? v(state.pdfUrl) : v })),
  setHasBoxes: (v) => set(state => ({ hasBoxes: typeof v === 'function' ? v(state.hasBoxes) : v })),
  setProductSearcher: (v) => set(state => ({ productSearcher: typeof v === 'function' ? v(state.productSearcher) : v })),
  setUndoStack: (v) => set(state => ({ undoStack: typeof v === 'function' ? v(state.undoStack) : v })),
  setRedoStack: (v) => set(state => ({ redoStack: typeof v === 'function' ? v(state.redoStack) : v })),
  setCanvasRotation: (v) => set(state => ({ canvasRotation: typeof v === 'function' ? v(state.canvasRotation) : v })),
  productSearcher: null,
  resetBomPdfExtractorStore: () => set(() => ({
    ...defaultStore,
    allBoxes: [],
    sourceCanvasRefs: [],
    scrollViewPort: null,
    updatedScrollViewPort: null,
    customBoxTypes: [],
    doResetBomPdfExtractorStore: { value: true },
    textractRBush: null,
  })),
}));
