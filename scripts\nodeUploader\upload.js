import { createReadStream, promises } from 'fs';
import drive, { RootFolderId } from './service.js';
import { basename } from 'path';
import { filesInDirectory, formatDate } from './helper.js';
import packageConfig from '../../package.json' assert { type: "json" };

// Define the folder structure (env/version/date/os)
const folderStructure = [
  process.env.NODE_ENV,
  'v' + packageConfig.version,
  formatDate(),
  process.platform ==='win32'? 'windows':  process.platform ==='darwin'?  'mac' : 'unknown-platform'
];

// Function to create folders recursively
async function createFolderIfNotExists(parentId, folders) {
  let modParentID = parentId
  for (const folderName of folders) {
    const existingFolder = await findOrCreateFolder(folderName, modParentID);
    modParentID = existingFolder.id;
  }
  return modParentID;
}

// Function to find or create a folder with a given name
async function findOrCreateFolder(folderName, parentId) {
  const query = `name='${folderName}' and '${parentId}' in parents and trashed=false`;

  const existingFolders = await drive().files.list({
    q: query,
    fields: 'files(id)',
  });

  if (existingFolders.data.files.length > 0) {
    return existingFolders.data.files[0];
  } else {
    const folderMetadata = {
      name: folderName,
      mimeType: 'application/vnd.google-apps.folder',
      parents: [parentId],
    };

    const newFolder = await drive().files.create({
      resource: folderMetadata,
      fields: 'id',
    });

    return newFolder.data;
  }
}

const uploadSingleFile = async (fileName, filePath) => {
  const parentFolderId = await createFolderIfNotExists(RootFolderId, folderStructure);

  return drive().files.create({
    resource: {
      name: fileName,
      parents: [parentFolderId],
    },
    media: {
      body: createReadStream(filePath),
    },
    fields: 'id,name',
  }).then(({ data: { id, name } = {} }) => {
    console.log('File Uploaded to ', folderStructure.join('/'), name, id);
  });
};

const scanFolderForFiles = async () => {
  const folders = filesInDirectory;
  for await (const dirent of folders) {
    const name = basename(dirent);
    const fullPath = dirent;
    await uploadSingleFile(name, fullPath);
  }
};

export default scanFolderForFiles;