.impersonate<PERSON><PERSON><PERSON>ontent {
    width: 100%;
    height: 520px;
    .impersonate<PERSON>ear<PERSON><PERSON>eader {
        height: 50px;
        align-self: stretch;
        flex-grow: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 1px;
        padding: 16px;
        background-color: #000;

        input {
            width: 100%;
            height: 40px;
            background-color: transparent;
            border: 0px;
            font-family: Noto Sans;
            font-size: 20px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: rgba(255, 255, 255, 0.5);

            &:focus {
                outline: none;
            }
        }
    }

    .userListTableContent{
        padding: 8px;
    }

    .tableGrid {
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 430px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        table {
            border-spacing: 0px;
            width: 100%;
            white-space: nowrap;
        }

        tr {
            th {
                height: 40px;
                padding: 2px 8px;
                background-color: #333;
                font-family: Inter;
                font-size: 14px;
                font-weight: 500;
                line-height: 1.4;
                text-align: left;
                color: #fff;
                position: sticky;
                top: 0px;
                border-top: 0.5px solid rgba(255, 255, 255, 0.6);
                border-bottom: 0.5px solid rgba(255, 255, 255, 0.6);

            }
        }

        tr {
            cursor: pointer;
            &:hover{
                background-color: #333;
            }
            &:last-child{
                td{
                    border-bottom: 0px;
                }
            }
            td {
                font-family: Inter;
                height: 40px;
                padding: 4px 8px;
                font-size: 14px;
                font-weight: 300;
                line-height: 1.2;
                text-align: left;
                color: #fff;
                vertical-align: middle;
                border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
            }
        }

    }
}