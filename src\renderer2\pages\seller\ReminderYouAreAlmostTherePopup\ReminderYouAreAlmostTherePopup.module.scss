.reminderPopup {
  .dialogContent {
    max-width: 520px;
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 45px 24px 40px 24px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    position: relative;
    background-image: linear-gradient(102deg, #0f0f14 -8%, #393e47 238%);

    .textLsines {
      margin-bottom: 15px;
      margin-left: 20px;
    }

    .closeIcon {
      position: absolute;
      top: 10px;
      right: 12px;
      cursor: pointer;
      opacity: 0.5;
      &:hover{
        opacity: unset;
      }

      svg {
        height: 20px;
        width: 20px;
        color: white;

        path {
          fill: #fff
        }
      }
    }

    .reminderPopupTitle {
      .titleText {
        font-family: Noto Sans;
        font-size: 20px;
        font-weight: 600;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        margin-bottom: 10px;
      }

      .titleSmallText {
        font-size: 14px;
        font-weight: normal;
        text-align: left;
        margin-bottom: 15px;
      }
    }

    .FormInputGroup {
      display: flex;
      margin-bottom: 8px;

      .lblInput {
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        height: 34px;
        display: flex;
        flex: 0 0 152px;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 6px 4px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(0, 0, 0, 0.25);
        border-radius: 4px 0px 0px 4px;
        border-right: 0px;
      }

      .lblAdress {
        flex: 0 0 152px;
        height: 68px;
        align-items: baseline;
      }

      .inputSection {
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 6px 6px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 0px 4px 4px 0px;
        flex: 1 auto;

        &.comanyName.comanyName{
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          text-align: left;
          color: #fff;
          padding: 0px;
          .comanyNameInput1.comanyNameInput1{
              padding: 6px 6px 6px 10px;
            }
        }

        &.companyState {
          flex: 0 0 96px
        }

        &.phoneNo {
          width: 128px;
        }

        &.bdrRadius0 {
          border-radius: 0px;
        }

        &.bdrTopRightRadius0 {
          border-top-right-radius: 0px;
        }

        &.bdrBtmRightRadius0 {
          border-bottom-right-radius: 0px;
        }

        &.bdrRight0 {
          border-right: 0px;
        }

        &.bdrBtm0 {
          border-bottom: 0px;
        }

        input {
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          text-align: left;
          color: #fff;
          border: 0px;
          background-color: transparent;
          padding: 0px;
          width: 100%;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;
          }
        }
      }

      &.FormInputGroupError {
        .lblInput {
          border: solid 0.5px #f00;
          background-color: #f00;
          cursor: pointer;
          white-space: nowrap;
        }

        .borderOfError {
          border: solid 0.5px #f00;
        }
      }

      .cityInput {
        flex: 0 0 128px;
      }

      .zipCodeInput {
        flex: 0 0 96px;
        padding: 6px 3px 6px 6px;

      }

    }

    .disclaimer {
      padding: 16px 24px;
      border-radius: 4px;
      background-color: #d9d9d9;
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.6;
      color: #000;
      text-align: center;
      position: relative;
      margin-bottom: 24px;
    }

    .noteText {
      margin-top: 10px;
    }

    .settingBtnMain {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      .saveSettingsBtn {
        transition: all 0.1s;
        font-family: Noto Sans;
        font-size: 16px;
        line-height: 1.6;
        text-align: center;
        color: #70ff00;
        background-color: transparent;
        border: 0;
        opacity: 0.7;

        &:disabled {
          cursor: not-allowed;
          color: #fff !important;
          opacity: 0.5 !important;
        }

        &:hover {
          color: #70ff00;
          opacity: unset;
        }
      }
    }
  }
}

.Dropdownpaper.Dropdownpaper {
  padding: 3px 4px 8px 8px;
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  background-color: rgba(255, 255, 255, 0.3);
  margin-top: 7px;
  overflow: hidden;
  width: 96px;
  border-radius: 0px 0px 4px 4px;
  margin-left: -3px;
  background: url(../../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  ul {
      overflow: auto;
      max-height: 230px;
      padding-right: 4px;
      padding-top: 0px;
      padding-bottom: 0px;

      &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
      }

      &::-webkit-scrollbar-thumb {
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 50px;
      }

      &::-webkit-scrollbar-track {
          background: transparent;
      }

      li {
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.4;
          text-align: left;
          color: #fff;
          border-radius: 2px;
          padding: 3px 5px;
          margin-bottom: 2px;

          &:hover {
              background-color: #fff;
              color: #000;
          }

          &[aria-selected="true"] {
              background-color: #fff;
              color: #000;
              border-radius: 2px;
          }
      }
  }
}

.containerPopup{
  width: 100%;
}

.successPopup{
  .dialogContent {
    max-width: 300px;
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 45px 24px 40px 24px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;
  }
  .closeIcon {
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;
    opacity: 0.5;

    &:hover {
      opacity: unset;
    }

    svg {
      height: 20px;
      width: 20px;
      color: white;

      path {
        fill: #fff
      }
    }
  }
  .successPopupMain{
    width: 100%;
    text-align: center;
  }
  .successText{
    font-family: Noto Sans;
    font-size: 18px;
    line-height: 1.6;
    text-align: center;
    color: #70ff00;
  }
  .btnsuccess{
    margin-top: 20px;
    margin-left: auto;
    margin-right: auto;
    width: 85px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 8px 24px;
    border-radius: 4px;
    border: solid 0.5px #fff;
    background-color: transparent;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    transition: all 0.1s;
    &:hover{
      background-color: #70ff00;
      border: solid 0.5px #70ff00;
      color: #000;
    }
  }
}

.errorMessage {
  padding: 4px 20px;
  border-radius: 50px;
  border: solid 1px #ff5d47;
  background-color: #ffefed;
  font-family: Noto Sans;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
  color: #ff5d47;
  display: flex;
  align-items: center;
  gap: 4px;
  position: absolute;
  left: 160px;
  top: 10px;
  svg{
    width: 20px;
    height: 20px;
  }
}

.autocompleteDescPanel {
  border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  -webkit-backdrop-filter: blur(24px);
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  background: url(../../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 0px 0px 4px 4px;
  margin-top: 1px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 300px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;

  }

  li {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 6px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;
    border-radius: 2px;

    &:hover {
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff !important;
      color: #000;
    }
  }
}

.companyInput {
  width: 100%;
  height: 100%;
  padding: 6px 6px 6px 10px;
  color: #fff;
  resize: none;
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: left;

  &::placeholder {
    color: #bbb;
  }

  &:focus-within {
    border: solid 1px #70ff00;
    outline: none;
    box-shadow: none;
    border-radius: 0px 2px 2px 0px;
  }
}