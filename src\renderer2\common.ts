import { uploadBomConst } from "@bryzos/giss-ui-library";

export const toggleStickyBtnId = 'toggle-sticky-btn';
export const localStorageStickyItemKey = 'isSticky';
export const SHOW_NOTIFICATION = "show-notification";
export const httpsOrigin = 'https://';
export const refererSuffix = '/';

export const userRole = {
  buyerUser : 'BUYER',
  sellerUser : 'SELLER',
  neutralUser : 'NEUTRAL'
}

export const chatUserRole = {
  USER: 'User',
  GUEST: 'Guest',
  MODERATOR: 'Moderator',
  ADMIN: 'Admin',
}

export const EmptyString = '';
export const MinSearchDataLen = 2;
export const keyUpCode = 38;
export const keyDownCode = 40;
export const keyEnterCode = 13;
export const keyUp = "ArrowUp";
export const keyDown = "ArrowDown";
export const keyEnter = "Enter";
export const commomKeys = {
  countryCode: '+1',
  uploadSuccessful: 'Upload Successful',
  error: 'Error',
  errorContent: 'Something went wrong. Please try again in sometime',
  errorBtnTitle: 'Ok',
  successBtnTitle: 'Got it',
  actionStatus: {
    success: 'success',
    error: 'error'
  },
  info: 'Info',
  refresh: 'Refresh',
  tryAgain: 'Try Again',
  downloadPath: 'downloads',
  continue: 'Continue'
}

export const snackbarSeverityType = {
  alert: 'alert',
  warning: 'warning',
  success: 'success'
}

export const snackbarMessageContent = {
  socketAuthExpiryMessage: "<p>Session expired. Please refresh the app or click on 'Try Again'</p>",
  socketRefreshMessage: "<p>We seem to have disconnected. Please click on 'Try Again'</p>",
  productReferenceDataChanged: "<p>Please refresh the app to get updated Products & Pricing</p>",
  discountPriceChanged: "<p>App improvements have been made that require an update.</p>"
}

export const referenceDataKeys = {
  domesticMaterialTextKey: "DOMESTIC_MATERIAL_ONLY_TEXT",
  sellerAvailInMinKey: "SELLER_AVAIL_IN_MINUTES"
}

export const routes = {
  loginPage : '/',
  homePage : '/home',
  forgotPassword : '/forgot-password',
  tempPage : '/share-widget',
  TnCPage : '/tnc',
  successPage : '/success',
  buyerSettingPage : '/buyer-setting',
  sellerSettingPage : '/seller-setting',
  createPoPage : '/create-po',
  orderConfirmationPage : '/order-confirmation',
  orderConfirmationPageSeller : '/order-confirmation-seller',
  acceptOrderPage : '/accept-order',
  disputePage : '/dispute',
  orderPage : '/order',
  newUpdate : '/new-update',
  onboardingWelcome:'/onboarding-welcome',
  onboardingTnc:'/onboarding-tnc',
  onboardingDetails:'/onboarding-details',
  onboardingThankYou:'/onboarding-thank-you',
  acceptedOrders: 'acceptedOrders',
  chat: '/chat',
  videoLibrary:'/video-library',
  impersonateList: '/impersonate-list',
  changePassword: '/change-Password',
  bomUpload: '/bom-upload',
  bomExtractor: '/v2/bom-extractor',
  bomUploadReview: '/bom-upload-review',
  subscribe: '/subscribe',
  savedBom: '/saved-bom',
  newSetting: '/new-setting',
}


export const DeliveryDates = [
    { title: 'Order Date + 2 Days', value: 2 },
    { title: 'Order Date + 3 Days', value: 3 },
    { title: 'Order Date + 4 Days', value: 4 },
    { title: 'Order Date + 5 Days', value: 5 },
    { title: 'Order Date + 6 Days', value: 6 },
    { title: 'Order Date + 7 Days', value: 7 },
    { title: 'Order Date + 8 Days', value: 8 },
    { title: 'Order Date + 9 Days', value: 9 },
    { title: 'Order Date + 10 Days', value: 10 },
    { title: 'Order Date + 11 Days', value: 11 },
    { title: 'Order Date + 12 Days', value: 12 },
    { title: 'Order Date + 13 Days', value: 13 },
    { title: 'Order Date + 14 Days', value: 14 },
    { title: 'Order Date + 15 Days', value: 15 },
    { title: 'Order Date + 16 Days', value: 16 },
  ];

  export const States = [
    { title: 'AK', value: 27 },
    { title: 'WD', value: 28 },
    { title: 'NY', value: 29 },
    { title: 'NE', value: 30 },
  ];
  
  
  export const reactQueryKeys = {
    cognitoUser: 'cognitoUser',
    getUserPartData: 'getUserPartData',
    getCassData: "getCassData",
    createCassSupplier: "createCassSupplier",
    getForbiddenTooltips: "getForbiddenTooltips",
    getCompanyLists: "getCompanyLists",
    getAcceptedOrders: "getAcceptedOrders",
    getSecurityData: "getSecurityData",
    getGameScore: "getGameScore",
    getGameSpeed: "getGameSpeed",
    getBomData: "getBomData",
    getSubscriptionsPricing: "getSubscriptionsPricing",
    getUserSubscription: "getUserSubscription",
    getUsersSavedBom: "getUsersSavedBom",
    getBuyingPreference: "getBuyingPreference",
    getSharedProductPrices: "getSharedProductPrices",
    getSharedAppHistory: "getSharedAppHistory",
    getSaveSearchProducts: "getSaveSearchProducts",
    getDeliveryAddress: "getDeliveryAddress",
  }


  export const RecevingHoursFrom = [
    {title:'3 am', value: 3, disabled: false},
    {title:'4 am', value: 4, disabled: false},
    {title:'5 am', value: 5, disabled: false},
    {title:'6 am', value: 6, disabled: false},
    {title:'7 am', value: 7, disabled: false},
    {title:'8 am', value: 8, disabled: false},
    {title:'9 am', value: 9, disabled: false},
    {title:'10 am', value: 10, disabled: false},
    {title:'11 am', value: 11, disabled: false},
    {title:'12 pm', value: 12, disabled: false},
    {title:'1 pm', value: 13, disabled: false},
    {title:'Closed', value: 'closed', disabled: false},
  ];

  export const RecevingHoursTo = [
    {title:'10 am', value: 10, disabled: false},
    {title:'11 am', value: 11, disabled: false},
    {title:'12 pm', value: 12, disabled: false},
    {title:'1 pm', value: 13, disabled: false},
    {title:'2 pm', value: 14, disabled: false},
    {title:'3 pm', value: 15, disabled: false},
    {title:'4 pm', value: 16, disabled: false},
    {title:'5 pm', value: 17, disabled: false},
    {title:'6 pm', value: 18, disabled: false},
    {title:'Closed', value: 'closed', disabled: false},
  ]

  export const ExpirationDate1 = [
    {title:'6 months', value: 12 },
    {title:'1 year', value: 13 },
    {title:'2 year', value: 14 },
    {title:'3 year', value: 15 },
    {title:'4 year', value: 16 },
    {title:'5 year', value: 17 },
    {title:'Never Expires', value: 18 }    
  ]

  export const ExpirationDate2 = [
    {title:'6 months', value: 12 },
    {title:'1 year', value: 13 },
    {title:'2 year', value: 14 },
    {title:'3 year', value: 15 },
    {title:'4 year', value: 16 },
    {title:'5 year', value: 17 },
    {title:'Never Expires', value: 18 }
  ]


 export const prefixUrl = {
    resaleCertPrefix : 'resalecert',
    irsW9Prefix : 'irsw9',
    lineCard : 'linecard',
    buyerPo : 'BuyerPO',
    sellerSo : 'SellerSO',
    uiLogs:'ui-logs'
  }

  export const fileType = {
    excelSheet : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    pdf: 'application/pdf'

  }

export const buyerSettingConst = {
  buyerCreditLineLimit : 200000,
  creditLimitErrorMessage: 'Currently credit lines up to $200,000 are supported.',
  uploadCertDialogContent: 'Your Resale Certificate has been uploaded successfully'
}
export const purchaseOrder = {
  readyToClaim: "READY_TO_CLAIM", 
  pending: "PENDING",
  paymentMethodACH: "ach_credit",
  paymentMethodBNPL: "bryzos_pay",
  hidden: "HIDDEN"
}

export const disputeConst = {
  searchPlaceholder: "Search Orders to Create Dispute",
}
export const orderConfirmationConst = {
  buyerCancel: "BUYER_ORDER_CANCEL",
  sellerCancel: "SELLER_ORDER_CANCEL",
  uploadPoDialogContent: 'Your Purchase Order has been uploaded successfully',
  uploadSoDialogContent: 'Your Sales Order has been uploaded successfully',
  getResaleListOfBuyerUseEffect: 'getResaleListOfBuyerUseEffect',
  handleCheckboxClick: 'handleCheckboxClick',
  uploadSoFile1: 'uploadSoFile1',
  uploadSoFile2: 'uploadSoFile2',
  uploadSoFile3: 'uploadSoFile3',
  downloadCertificate: 'downloadCertificate',
  cancelOrder: 'cancelOrder',
}

export const orderPageConst = {
  orderNotAvaialableMsg: "The order is no longer available.",

}

export const userTypes = [
  { name: 'Buyer', value: 'BUYER'},
  { name: 'Seller', value: 'SELLER'},
];

export const supplier = "Supplier";
export const cassErrorMessage = "Cass - ";

export const CUSTOM_NOTIFICATION_PRIORTY = ["LOW", "MEDIUM", "HIGH"];
export const CUSTOM_NOTIFICATION_ACTION = ["REFRESH", "CLOSE"];

export const pdfMakeData = {
  codes: {
    success: 'success',
    cancel: 'cancel',
    ebusy: 'EBUSY',
  },
  message: {
    success: 'PDF successfully generated and saved.',
    error: 'Error in generating PDF'
  },
  pdfName: 'order_review',
}
export const PDF_TEMPLATE_EXPORT = 'TemplateExportPdf';

export const raygunKeys = {
  socketInvalidToken: {
    tag: "socket-invalid-token",
    errorMsg : "Error in socket connection with invalid token."
  }
}

export const changePasswordConst = {
  onSuccess: 'Successfully changed password',
  onError: 'Error changing password',
  noUserCredentialFound: 'No Credentials Found'
}

export const defaultResaleCertificateLine = {state_id: "", expiration_date: "", uploadCertProgress: null, is_deletable: true };

export const sellerSettingConstant = {
  sellerSettingUseEffect: 'sellerSettingUseEffect',
  uploadIRSW9File1:'uploadIRSW9File1',
  uploadIRSW9File2:'uploadIRSW9File2',
  uploadLineCardFile1:'uploadLineCardFile1',
  uploadLineCardFile2:'uploadLineCardFile2',
  getTruevaultData1:'getTruevaultData1',
  getTruevaultData2:'getTruevaultData2',
  submitData:'submitData'
}

export const acceptOrderConst = {
  acceptOrderPoIndexUseEffect: 'acceptOrderPoIndexUseEffect'
}

export const shareProductPricingOrAppConst = {
  handleSubmitData: 'handleSubmitData'
}

export const productSearchConst = {
  searchAnalyticsApi: 'searchAnalyticsApi'
}

export const homePageConst = {
  onShareApp: 'onShareApp',
  onShareProductPricing: 'onShareProductPricing'

}

export enum AuthError {
  EmailNotFound = "EmailNotFound",
  PasswordMismatch = "PasswordMismatch",
  LoginFailed = "LoginFailed",
  LoginApprovalError = "LoginApprovalError",
}

export const options = [
  {
      title: "YES",
      value: true
  }, 
  {
      title: "NO",
      value: false
  }];

export const units = [
      {
          title: "FT",
          value: "ft"
      }, 
      {
          title: "PC",
          value: "pc"
      },
      {
          title: "LB",
          value: "lb"
      },
      {
          title: "CWT",
          value: "cwt"
      },
  ];

  
export const feedbackUnits = [
  {
      title: "PC",
      value: "pc"
  },
  {
    title: "LB",
    value: "lb"
  },
  {
    title: "CWT",
    value: "cwt"
  },
  {
      title: "FT",
      value: "ft"
  }
];
export const cognitoCookiePrefix = 'CognitoIdentityServiceProvider';
export const systemVersionWebConst = 'CLOUDFRONT WEB';

export const MAX_APP_HEIGHT = 1060;
export const MAX_APP_WIDTH = 1564;

export const navigationConfirmMessages = {
  unsavedChanges: 'Your changes will be lost if you leave this page. Do you want to continue?',
  confirmLeave: 'Are you sure you want to leave this page?'
};

export const BNPLSTATUS = {
  ENABLED: 'ENABLED',
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  ON_HOLD: "ON HOLD",
  RESTRICTED: "RESTRICTED"
};

export const localStorageKeys = {
  bomData: "bomData",
  lastModifiedBom: "lastModifiedBom"
}

export const bomLineStatusCountObjDefault = {
  [uploadBomConst.lineItemStatus.pending]: 0,
  [uploadBomConst.lineItemStatus.approved]: 0,
  [uploadBomConst.lineItemStatus.skipped]: 0,
  [uploadBomConst.lineItemStatus.deleted]: 0
}

export const shareEmailTypes = {
  sharePrice: 'sharePrice',
  inviteUser: 'inviteUser'
}


// Common function for formatting numeric values with smart decimal handling
export const formatNumericValue = (value: string | null | undefined, decimalPlaces: 2 | 4 = 4): string => {
  if (!value) return '';
  
  const num = Number(value);
  const formatted = num.toLocaleString(undefined, { 
    minimumFractionDigits: decimalPlaces, 
    maximumFractionDigits: decimalPlaces 
  });
  
  // If all decimal places are zeros (e.g., 2500.0000), show as "2,500"
  if (num % 1 === 0) {
    return num.toLocaleString(undefined, { maximumFractionDigits: 0 });
  }
  
  // Remove trailing zeros after decimal point
  const [whole, decimal] = formatted.split('.');
  const trimmedDecimal = decimal.replace(/0+$/, '');
  
  // If there are no significant decimal digits left, return whole part only
  if (trimmedDecimal === '') {
    return whole;
  }
  
  // Otherwise return with the significant decimal places
  return `${whole}.${trimmedDecimal}`;
};

export const LargeProductsNameList = ['Miscellaneous', 'Unequal'];

export const hoverVideoAttribute = 'data-hover-video-id';