import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

if(!process.env.NODE_ENV)
throw new Error('NODE_ENV is required')

const readFile = path.join(__dirname, `../config-electron/config.${process.env.NODE_ENV}.json`);
const writeFile = path.join(__dirname, '../src/main/config.js')

const jsonString = fs.readFileSync(readFile, 'utf-8');
 
const content = `
// this is is auto generated and should not be edited manually, can be created using configMoveElectron.mjs
const config = ${jsonString}

export default config;
`

fs.writeFileSync(writeFile, content)