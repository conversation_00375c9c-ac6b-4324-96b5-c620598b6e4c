import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import Cass from 'src/renderer2/pages/buyer/Cass';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import TrueVaultClient from 'truevault';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';

interface InputFocusState {
  bankName1: boolean;
  routingNo: boolean;
  accountNo: boolean;
  remittanceEmail: boolean;
}

const PaymentsTab: React.FC = ({ control, errors, setError, setValue, watch, isDirty, isValid, register, handleSubmit, getValues, trigger, isBnplFilled, clearErrors, achId, wireId, dirtyFields , resetField }: any) => {
  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    bankName1: false,
    routingNo: false,
    accountNo: false,
    remittanceEmail: false
  });
  const referenceData: any = useGlobalStore(state => state.referenceData);
  const userData: any = useGlobalStore(state => state.userData);
  const [states, setStates] = useState([]);
  const {setShowLoader} = useGlobalStore();
  const { mutate: saveUserSettings } = useSaveUserSettings();

  useEffect(() => {
    if (referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  const paymentCreditReq = useRef(null);
  const childRef = useRef();

  const fundingSettingChanged = (field: any) => {
    const bankName1 = getValues("bankName1");
    const routingNo = getValues("routingNo");
    const accountNo = getValues("accountNo");
    if (field === "bankName1") {
      if (routingNo?.includes("x")) {
        setValue('routingNo', "");
      }
      if (accountNo?.includes("x")) {
        setValue('accountNo', "");
      }

    } else if (field === "routingNo") {
      if (bankName1?.includes("x")) {
        setValue('bankName1', "");
      }
      if (accountNo?.includes("x")) {
        setValue('accountNo', "");
      }
      if(routingNo.includes("x")) {
        setValue('routingNo', "");
      }
    } else if (field === "accountNo") {
      if (bankName1?.includes("x")) {
        setValue('bankName1', "");
      }
      if (routingNo?.includes("x")) {
        setValue('routingNo', "");
      }
      if(accountNo.includes("x")) {
        setValue('accountNo', "");
      }
    }
  }

  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, paymentId) => {
    try {
      const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
      const accessToken = res.data.data;
      const sellerPaymentData = {
        "document": {
          "company_name": companyName,
          "user_id": userData,
          "bank_name": bankName,
          "routing_number": routingNo,
          "account_number": accountNo,
          "pgpm_mapping_id": paymentId
        }
      }

      const client = new TrueVaultClient({ accessToken });

      try {
        const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID, null, sellerPaymentData)
        const documentIdFromTruevault = response.id;
        return documentIdFromTruevault;

      } catch (error) {
        console.error(error);
      }
    } catch (error) {
      console.error(error);
    }
  }

  const handleSavePayment = async () => {
    try {
    if (watch('bankName1') && watch('routingNo') && watch('accountNo') && await trigger('bankName1') && await trigger('routingNo') && await trigger('accountNo')) {
      setShowLoader(true);
      const fieldsToReset = ['bankName1', 'routingNo', 'accountNo'];
      fieldsToReset.forEach((fieldName) => {
        const currentValue = watch(fieldName);
        resetField(fieldName, { 
          defaultValue: currentValue,
          keepError: false,
          keepDirty: false,
          keepTouched: true
        });
      });
      if (referenceData?.ref_general_settings.length) {
        let obj = referenceData.ref_general_settings.find((obj) => obj.name === "CASS_MASTER_DATA_CREATE");
        if (obj?.value === "true") {
          try {
            const result = await childRef.current.startCassCreateion();
          } catch (error) {
            console.error("error", error)
            setShowLoader(false)
          }
        }
      }

      if (isNaN(watch("routingNo")) || isNaN(watch("accountNo"))) {
        const key = isNaN(watch("routingNo")) ? 'routingNo' : 'accountNo'
        setError(key, { message: 'ACH Credit is not valid' }, { shouldFocus: true });
        return
      }
      setShowLoader(true);
      getTruevaultData(watch("companyName"), userData.data.id, watch("bankName1"), watch("routingNo"), watch("accountNo"), achId).then(documentIdFromTruevault => {
        const achFundingSetting = {};
        const convertedRoutingNo = watch("routingNo").slice(-4).padStart(watch("routingNo").length, 'x');
        const convertedAccountNO = watch("accountNo").slice(-4).padStart(watch("accountNo").length, 'x');
        achFundingSetting.bank_name = watch("bankName1");
        achFundingSetting.routing_number = convertedRoutingNo;
        achFundingSetting.account_number = convertedAccountNO;
        achFundingSetting.reference_document_id = documentIdFromTruevault;
        achFundingSetting.pgpm_mapping_id = achId;
        submitData(achFundingSetting);
      })
    }
    } catch (error) {
      console.log("error", error)
      setShowLoader(false)
    }
  }

  const submitData = async (data: any) => {
    try {
      saveUserSettings({ route: 'user/seller/settings/funding', data: { funding_settings: data } })
    } catch (error) {
      console.log("error", error)
    } finally {
      setShowLoader(false)
    }
  }

  const handleAchInputBlur = async (fieldName: any) => {
    if (dirtyFields[fieldName]) {
      setTimeout(() => {
        handleSavePayment()
      }, 100)
    }
  }

  const handleInputBlur = (fieldName: any) => {
    if (dirtyFields[fieldName]) {
      setTimeout(() => {
        handleSavePaymentSettings()
      }, 100)
    }
  }

  const handleSavePaymentSettings = async () => {
    try {
      const userSettingsPayload: any = {};
      if (watch('remittanceEmail') && watch('remittanceEmail') !== "") {
        const isValid = await trigger('remittanceEmail');
        if (isValid) {
          userSettingsPayload.remittance_email = watch('remittanceEmail');
        }
        if (Object.keys(userSettingsPayload).length > 0) {
          saveUserSettings({ route: 'user/seller/settings/funding', data: userSettingsPayload })
          resetField('remittanceEmail', {
            defaultValue: watch('remittanceEmail'),
            keepError: false,
            keepDirty: false,
            keepTouched: true
          });
        }
      }
    } catch (error) {
      console.log("error", error)
    } finally {
      setShowLoader(false)
    }
  }

  // Add onChange validation for ACH fields
  const handleAchFieldChange = async (fieldName: string, e: any) => {
    register(fieldName).onChange(e);
    setValue('achCheckBox', true);
    fundingSettingChanged(fieldName);
  }

  return (
    <div className={clsx(styles.tabContent, styles.paymentTabContent)} ref={paymentCreditReq}>
      <div className={styles.scrollerContainer}>

        <div className={styles.formContainer}>
          <span className={styles.paymentHeaderText}> Enter your company’s bank information to be funded by Bryzos via ACH Credit. All invoices from your company must be emailed to 
            <span className={styles.textBold}> <EMAIL></span> to be recorded for payment. All information will be encrypted immediately upon entry.</span>
          <div className={styles.formGroupInputContainer}>
            <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={styles.paymentHeader} htmlFor="bankName1">
                  ACH CREDIT
                </label>
              </span>
              <span className={styles.col1}>
              </span>
            </div>
            <>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.bankName1 && styles.focusLbl)} htmlFor="bankName1">
                    BANK NAME
                  </label>
                </span>
                <span className={styles.col1}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.bankName1 && styles.error)}
                      type='text'
                      register={register("bankName1")}
                      placeholder=''
                      onChange={(e: any) => {
                        handleAchFieldChange("bankName1", e)
                      }}
                      onBlur={() => {
                        handleAchInputBlur("bankName1")
                      }}
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.routingNo && styles.focusLbl)} htmlFor="routingNo">
                    BANK ROUTING NUMBER
                  </label>
                </span>
                <span className={styles.col1}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.routingNo && styles.error)}
                      type='text'
                      register={register("routingNo")}
                      placeholder=''
                      maxLength={9}
                      mode="maskedWholeNumber"
                      onChange={(e) => {
                        handleAchFieldChange("routingNo", e)
                      }}
                      onBlur={() => {
                        handleAchInputBlur("routingNo")
                      }}
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.accountNo && styles.focusLbl)} htmlFor="accountNo">
                    BANK ACCOUNT NUMBER
                  </label>
                </span>
                <span className={styles.col1}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.accountNo && styles.error)}
                      type='text'
                      register={register("accountNo")}
                      mode="maskedWholeNumber"
                      placeholder=''
                      onChange={(e) => {
                        handleAchFieldChange("accountNo", e)
                      }}
                      onBlur={() => {
                        handleAchInputBlur("accountNo")
                      }}
                    />
                  </InputWrapper>
                </span>
              </div>
              <div className={styles.formGroupInput}>
                <span className={styles.col1}>
                  <label className={clsx(isInputFocused.remittanceEmail && styles.focusLbl)} htmlFor="remittanceEmail">
                    REMITTANCE EMAIL
                  </label>
                </span>
                <span className={styles.col1}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.remittanceEmail && styles.error)}
                      type='email'
                      register={register("remittanceEmail")}
                      placeholder=''
                      onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register("remittanceEmail").onBlur(e);
                        handleInputBlur('remittanceEmail')
                      }}
                      errorInput={errors?.remittanceEmail}
                    />
                  </InputWrapper>
                </span>
              </div>
            </>


          </div>
        </div>
        <Cass ref={childRef} getValues={getValues} referenceData={referenceData} states={states} containerRef={paymentCreditReq.current} />

      </div>
    </div>
  );
};

export default PaymentsTab; 