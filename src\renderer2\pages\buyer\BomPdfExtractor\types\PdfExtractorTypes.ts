import { RefObject } from 'react';

// Common types for both PdfTextExtractor and PdfTextExtractorOCR

export interface Point {
  x: number;
  y: number;
}

export interface Rect {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface Box {
  id: string;
  type: string;
  rect: Rect;
  page: number;
  extractedText?: string;
  confidence?: number;
}

export interface BoxStyle {
  color: string;
  fillColor: string;
}

export interface BoxTypes {
  [key: string]: {
    id: string;
    label: string;
    color: string;
    fillColor: string;
  };
}

export interface ExtractedData {
  [key: string]: any;
}

export interface PageRotations {
  [key: string]: number;
}

export interface FineRotations {
  [key: string]: number;
}

export interface RawBoxData {
  [key: string]: any;
}

export interface AllBoxes {
  [key: string]: Box[];
}

export interface AllExtractedData {
  [key: string]: ExtractedData;
}

export interface RenderRef {
  isFirstRender: boolean;
  lastPageNumber: number | null;
  lastScale: number | null;
  lastRotation: number | null;
  lastPdfFile: File | null;
  lastZoom: number;
}

export interface CanvasSize {
  width: number;
  height: number;
}

export interface TextPosition {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontName?: string;
  fontSize?: number;
}

export interface BoundingBox {
  Width: number;
  Height: number;
  Left: number;
  Top: number;
}

export interface TextractBlock {
  Id: string;
  BlockType: string;
  Confidence: number;
  Text?: string;
  BoundingBox?: BoundingBox;
  [key: string]: any;
}

export interface TextractResult {
  Blocks: TextractBlock[];
  DocumentMetadata?: {
    Pages: number;
  };
  [key: string]: any;
}

export interface ProcessedTextractData {
  [key: string]: any;
}

// Props for PdfTextExtractor
export interface PdfTextExtractorProps {
  pdfFile: File;
  onExtractionComplete?: (result: TextractResult) => void;
  onError?: (error: Error) => void;
  onBoxesChange?: (hasBoxes: boolean) => void;
  onExtractedDataChange?: (hasData: boolean) => void;
  currentBoxType?: string;
  snapToGrid?: boolean;
  gridOpacity?: number;
  showMagnifyingGlass?: boolean;
}

// Additional props for PdfTextExtractorOCR
export interface PdfTextExtractorOCRProps extends PdfTextExtractorProps {
  autoSelectColumns?: boolean;
  overlapPercent?: number;
}

// Ref methods exposed by both components
export interface PdfExtractorRefMethods {
  extractText: () => Promise<TextractResult>;
  reset: () => void;
  handleUndoBox: () => void;
  extractImages: () => void;
  exportCSV: () => void;
  setCurrentBoxType: (type: string) => void;
  initializeTextractRBush: (textractData: TextractResult) => TextractRBushInstance;
}

// TextractRBush interface
export interface TextractRBushInstance {
  initialize: (textractData: TextractResult) => TextractRBushInstance;
  query: (rect: Rect, overlapRatio: number, pageSize: { width: number, height: number }, page?: number, rotation?: number, zoomFactor?: number) => any[];
  findTable: (cell: any) => any;
  [key: string]: any;
}
