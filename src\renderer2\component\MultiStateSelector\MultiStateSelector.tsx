import React, { useState, useEffect } from 'react';
import styles from './MultiStateSelector.module.scss';
import clsx from 'clsx';

interface MultiStateSelectorProps {
  states: Array<{ id: number; code: string }>;
  selectedStates: number[];
  onSelectionChange: (selectedStates: number[]) => void;
  onUpdateStates?: (selectedStates: number[]) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  error?: any;
  className?: string;
}

const MultiStateSelector: React.FC<MultiStateSelectorProps> = ({
  states,
  selectedStates,
  onSelectionChange,
  onUpdateStates,
  onFocus,
  onBlur,
  error,
  className,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const filteredStates = states.filter((state) =>
    state.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleStateToggle = (stateId: number) => {
    const newSelection = selectedStates.includes(stateId)
      ? selectedStates.filter((id) => id !== stateId)
      : [...selectedStates, stateId];
    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    onSelectionChange(filteredStates.map((state) => state.id));
  };

  const handleDeselectAll = () => {
    onSelectionChange([]);
  };

  const handleUpdateStates = () => {
    onUpdateStates?.(selectedStates);
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Only blur if focus is moving outside the component
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsFocused(false);
      onBlur?.();
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  return (
    <div
      className={clsx(
        styles.multiStateSelector,
        className,
        error && styles.error
      )}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0}
    >
      {/* Search input */}
      <div className={styles.searchContainer}>
        <input
          type='text'
          placeholder='Search states...'
          value={searchTerm}
          onChange={handleSearchChange}
          className={styles.searchInput}
        />
        {searchTerm && (
          <button
            type='button'
            onClick={clearSearch}
            className={styles.clearSearch}
          >
            ×
          </button>
        )}
      </div>

      {/* Control buttons */}
      <div className={styles.controlButtons}>
        <button
          type='button'
          onClick={handleSelectAll}
          className={styles.controlButton}
          disabled={filteredStates.length === 0}
        >
          Select All
        </button>
        <button
          type='button'
          onClick={handleDeselectAll}
          className={styles.controlButton}
          disabled={selectedStates.length === 0}
        >
          Deselect All
        </button>
        <button
          type='button'
          onClick={handleUpdateStates}
          className={clsx(styles.controlButton, styles.updateButton)}
          disabled={!onUpdateStates}
        >
          Update States
        </button>
      </div>

      {/* States grid */}
      <div className={styles.statesGrid}>
        {filteredStates.map((state) => (
          <div
            key={state.id}
            className={clsx(
              styles.stateItem,
              selectedStates.includes(state.id) && styles.selected
            )}
            onClick={() => handleStateToggle(state.id)}
          >
            <input
              type='checkbox'
              checked={selectedStates.includes(state.id)}
              onChange={() => handleStateToggle(state.id)}
              className={styles.stateCheckbox}
              tabIndex={-1}
            />
            <span className={styles.stateCode}>{state.code}</span>
          </div>
        ))}
      </div>

      {/* Selected count and no results message */}
      {filteredStates.length === 0 && searchTerm ? (
        <div className={styles.noResults}>
          No states found matching "{searchTerm}"
        </div>
      ) : (
        <div className={styles.selectedCount}>
          {selectedStates.length} state{selectedStates.length !== 1 ? 's' : ''}{' '}
          selected
          {searchTerm &&
            ` (${
              filteredStates.filter((state) =>
                selectedStates.includes(state.id)
              ).length
            } visible)`}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className={styles.errorMessage}>
          {error.message || 'Invalid selection'}
        </div>
      )}
    </div>
  );
};

export default MultiStateSelector;
