export class ProductSearch {
    constructor() {
        // Initialize the product search
    }

    async search(item){
        let description = item?.description ?? "";
        const specification = item?.specification ?? "";
        let grade = item?.grade ?? "";
        const length = item?.length?.toString() ?? "";
        // We're not using weight_per_quantity in this method, but it's available in the item object
        // const weight_per_quantity = item?.weight_per_quantity ?? "";

        // If specification and grade are the same, set grade to null
        if(specification === grade){ grade = ""; }

        // Remove grade from description if it's already included
        if(description.includes(grade) && isNaN(Number(grade)) ){
            description = description.replace(grade,"");
        }

        // Remove length from description if it's already included
        if( length != "" && ( length.includes("'") || length.includes('ft') || length.includes('feet') || length.includes('foot') ) ){
            description = description.replace(length, "");
        }else if(!length || length == ''){
            //no length provided
        }

        // Combine description and specification for processing
        let userInput = description + " " + specification;

        // Normalize the product description
        // Replace x with spaces around it for better parsing and remove commas
        let productDescription = userInput.toLowerCase().trim()
            .replace(/(\S)x/g, '$1 x ')  // Add space before x if preceded by non-space
            .replace(/x(\S)/g, 'x $1')   // Add space after x if followed by non-space
            .replace(/,/g, "");

                // Check for PL/FT or PL-FT notation which is common in carbon steel products
        if (productDescription.includes("pl/ft") || productDescription.includes("pl-ft") || productDescription.includes("plft") || productDescription.includes("plain per ft")) {
            // This is a "Plain per Foot" notation, not necessarily a plate
            // We'll extract this information but not use it to determine shape
            productDescription = productDescription.replace(/pl[\/-]ft|plft|plain per ft/gi, "");
        }


        if (!productDescription || productDescription === "") {
            return null;
        }

        let shapeType = null;
        let category = null;

        // Create a more comprehensive noSpaceDescription for pattern matching
        const noSpaceDescription = productDescription.replace(/\s+/g, "");

        // Process common steel shape abbreviations
        // This section identifies shape types and categories from standard abbreviations

        // Define a mapping of abbreviations to their properties
        const shapeAbbreviations = [
            // [pattern, replacement, shapeType, category]
            [/(?<![a-zA-Z])ts(?![a-zA-Z])/, 'sheet ', 'sheet', 'tread'],       // TS = Tube Steel/Sheet
            [/(?<![a-zA-Z])sht(?![a-zA-Z])/, 'sheet ', 'sheet', null],    // SHT = Sheet
            [/(?<![a-zA-Z])fb(?![a-zA-Z])/, 'bar ', 'bar', 'fb'],         // FB = Flat Bar
            [/(?<![a-zA-Z])sb(?![a-zA-Z])/, 'bar ', 'bar', 'sb'],         // SB = Square Bar
            [/(?<![a-zA-Z])rb(?![a-zA-Z])/, 'bar ', 'bar', 'rb'],         // RB = Round Bar
            [/(?<![a-zA-Z])l(?![a-zA-Z])/, 'angle ', 'angle', 'l'],       // L = Angle
            [/(?<![a-zA-Z])pl(?![a-zA-Z])/, 'plate ', 'plate', 'pl'],     // PL = Plate
            [/(?<![a-zA-Z])tp(?![a-zA-Z])/, 'plate ', 'plate', null],     // TP = Thick Plate
            [/(?<![a-zA-Z])w(?![a-zA-Z])/, 'beam ', 'beam', 'wf'],        // W = Wide Flange Beam
            [/(?<![a-zA-Z])wf(?![a-zA-Z])/, 'beam ', 'beam', 'wf'],       // WF = Wide Flange Beam
            [/(?<![a-zA-Z])ang(?![a-zA-Z])/, 'angle ', 'angle', null],    // ANG = Angle
            [/(?<![a-zA-Z])misc(?![a-zA-Z])/, 'misc ', 'misc', 'channel'], // MISC = Miscellaneous (often channels)
            [/(?<![a-zA-Z])mc(?![a-zA-Z])/, 'channel ', 'channel', 'mc'],  // MC = Miscellaneous Channel
            [/(?:^|\s)c(?:\s|$)/, 'channel ', 'channel', null]            // C = Channel
        ];

        // Process each abbreviation pattern
        for (const [pattern, replacement, shape, cat] of shapeAbbreviations) {
            if (pattern.test(productDescription)) {
                productDescription = productDescription.replace(pattern, replacement);
                shapeType = shape;
                if (cat) category = cat;
            }
        }

        // Special handling for HSS (Hollow Structural Section) pattern
        if (/(?<![a-zA-Z])hss(?![a-zA-Z])/.test(productDescription)) {
            shapeType = "hss";
            // Try to determine if it's square or rec
            if (productDescription.includes("square") || noSpaceDescription.includes("square")) {
                category = "square";
            } else if (productDescription.includes("rect") || noSpaceDescription.includes("rect")) {
                category = "rec";
            }
        }

        // Check for specific carbon steel product patterns

        // Define common product patterns and their shape/category mappings
        const productPatterns = [
            // [pattern test function, shapeType, category, description]
            [
                desc => desc.includes("hr flat") || desc.includes("hot rolled flat"),
                "bar", "fb", "Hot Rolled Flat Bar"
            ],
            [
                desc => desc.includes("square tube") || desc.includes("sq tube") || desc.includes("sqtube"),
                "hss", "square", "Square Tube"
            ],
            [
                desc => desc.includes("struc channel") || desc.includes("structural channel"),
                "channel", "structural", "Structural Channel"
            ],
            [
                desc => desc.includes("wf beam") || desc.includes("wide flange"),
                "beam", "wf", "Wide Flange Beam"
            ]
        ];

        // Process each product pattern
        for (const [testFn, shape, cat, _] of productPatterns) {
            if (testFn(productDescription)) {
                shapeType = shape;
                category = cat;
            }
        }

        // If shape type is not determined yet, check for common shape keywords
        if (!shapeType) {
            // Define shape keyword patterns and their properties
            const shapeKeywords = [
                // [test function, shapeType, categoryFn, description]
                [
                    desc => desc.includes("channel") || noSpaceDescription.includes("channel"),
                    "channel", null, "Channel"
                ],
                [
                    desc => desc.includes("beam") || noSpaceDescription.includes("beam") ||
                           desc.includes("wf") || noSpaceDescription.includes("wfb"),
                    "beam",
                    desc => (desc.includes("wf") || noSpaceDescription.includes("wfb")) ? "wf" : null,
                    "Beam/Wide Flange"
                ],
                [
                    desc => desc.includes("plate") || noSpaceDescription.includes("plate"),
                    "plate", null, "Plate"
                ],
                [
                    desc => desc.includes("angle") || noSpaceDescription.includes("angle"),
                    "angle", null, "Angle"
                ],
                [
                    desc => desc.includes("pipe") || noSpaceDescription.includes("pipe"),
                    "pipe", null, "Pipe"
                ],
                [
                    desc => (desc.includes("rec") || desc.includes("sq")) && desc.includes("wall") ||
                           desc.includes("hss") || desc.includes("tub") ||
                           noSpaceDescription.includes("hss") || desc.includes("square tube") ||
                           desc.includes("rec tube"),
                    "hss",
                    desc => {
                        if (desc.includes("square") || desc.includes("sq tube")) return "square";
                        if (desc.includes("rec") || desc.includes("rec tube")) return "rec";
                        return null;
                    },
                    "Hollow Structural Section"
                ],
                [
                    desc => desc.includes("bar") || noSpaceDescription.includes("bar") ||
                           desc.includes("flat") || noSpaceDescription.includes("flat") ||
                           desc.includes("hr flat"),
                    "bar",
                    desc => {
                        if (desc.includes("flat bar") || desc.includes("hr flat")) return "fb";
                        if (desc.includes("round bar")) return "rb";
                        if (desc.includes("square bar")) return "sb";
                        return null;
                    },
                    "Bar"
                ],
                [
                    desc => desc.includes("sheet") || desc.match(/\b\d+\s*ga\.?\b/i) ||
                           noSpaceDescription.includes("sheet"),
                    "sheet", null, "Sheet"
                ],
                [
                    desc => desc.includes("coil") || desc.includes("galv") ||
                           noSpaceDescription.includes("coil"),
                    "coil", null, "Coil"
                ],
                [
                    desc => desc.match(/\bfl\s+\d/) || noSpaceDescription.includes("fl"),
                    "bar", () => "fb", "Flat Bar (FL format)"
                ]
            ];

            // Process each shape keyword pattern
            for (const [testFn, shape, categoryFn, _] of shapeKeywords) {
                if (testFn(productDescription)) {
                    shapeType = shape;
                    if (categoryFn) {
                        const cat = categoryFn(productDescription);
                        if (cat) category = cat;
                    }
                    break; // Stop after first match
                }
            }
        }

        // If we still don't have a shape type, try to infer from the dimensions pattern
        if (!shapeType) {
            // Define dimension patterns for different shapes
            const dimensionPatterns = [
                // [pattern, shapeType, category, description]
                [/\d+\s*x\s*\d+\s*x\s*\d+\/\d+/i, "angle", null, "Angle pattern: typically 3 dimensions like 2x2x3/16"],
                [/\d+\/\d+\s*x\s*\d+(?:-\d+\/\d+)?/i, "bar", "fb", "Flat/Bar pattern: typically 2 dimensions with one being a fraction like 1/2x1-3/4"],
                [/\d+\.\d+\s*(?:in|")|(?:\d+\.\d+\s*x\s*\d+\.\d+)/i, "pipe", null, "Pipe pattern: typically has a decimal diameter and wall thickness like 2.50in or 2.875x0.552"],
                [/(\d+)\s*x\s*\1\s*x\s*\d+\/\d+/i, "hss", "square", "Square tube pattern: typically has equal dimensions like 5x5x1/2"]
            ];

            // Process each dimension pattern
            for (const [pattern, shape, cat, _] of dimensionPatterns) {
                if (pattern.test(productDescription) && !shapeType) {
                    shapeType = shape;
                    if (cat) category = cat;
                    break; // Stop after first match
                }
            }
        }

        try{
            const result = await this.tryParseShape(productDescription, specification, grade, length.toLowerCase(), shapeType, category);
            console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>result : ", result);
            return result;
        }catch(error){
            console.log("error : ", error);
            return null;
        }
    }

    async normalizeInput(text) {
        text = text
            .toLowerCase()
            .replace(/–/g, "")
            .replace(/#/g, "Lb")
            .replace("a-", "a")
            .replace("sq.", "sq")
            .replace(/(?<=^|\s)(\d+)\s+(\d+\/\d+)/g, "$1-$2")
            .replace(/(\d+)inches/g, "$1\"") // Convert any number followed by "inches" to number followed by quotation mark
            .replace(/(\d+)inch/g, "$1\"") // Convert any number followed by "inch" to number followed by quotation mark
            .replace(/(\d+)in/g, "$1\"") // Convert any number followed by "in" to number followed by quotation mark
            .replace(/\(/g, "")
            .replace(/\)/g, "")
            .trim();

        return text;
    }

    async figureOutDimensionsFromAlphabets(text) {
        console.log("figureOutDimensionsFromAlphabets : ", text);
        // Check if the text contains alphabets that might be part of dimensions
        if (!/[a-zA-Z]/.test(text)) {
            return text; // No alphabets found, return as is
        }

        // First, identify and protect specification patterns
        // This will prevent us from replacing letters in specifications like A992, A36, etc.
        const specPatterns = [
            /\b[aA][\/-]?\d+(?:\/[aA][\/-]?\d+)?(?:[\/-]?[a-zA-Z\d]+)?\b/g  // Matches A36, A-36, A36/A572-50, etc.
        ];

        // Create a mapping to store protected sections and their replacements
        const protectedSections = new Map();
        let protectionIndex = 0;

        // Replace spec patterns with placeholder tokens
        let protectedText = text;
        for (const pattern of specPatterns) {
            protectedText = protectedText.replace(pattern, (match) => {
                const token = `__PROTECTED_${protectionIndex++}__`;
                protectedSections.set(token, match);
                return token;
            });
        }

        // Create a mapping of common alphabet-to-number conversions
        const alphabetToNumber = {
            'o': '0', 'O': '0',
            'l': '1', 'L': '1', 'I': '1', 'i': '1',
            'z': '2', 'Z': '2',
            'e': '3', 'E': '3',
            'a': '4', 'A': '4',
            's': '5', 'S': '5',
            'g': '6', 'G': '6',
            't': '7', 'T': '7',
            'b': '8', 'B': '8',
            'q': '9', 'Q': '9', 'g': '9', 'G': '9'
        };

        // Replace alphabets with their number equivalents
        let result = protectedText;

        // First, handle specific dimension patterns with potential letter-to-number conversions
        // This helps with common patterns like "l/2" (should be "1/2") or "l6 x 4" (should be "16 x 4")
        const dimensionPatterns = [
            // Match patterns like "l/2" or "l/4" (should be "1/2" or "1/4")
            { regex: /([a-zA-Z])\/(\d+)/g, letterIndex: 1 },
            // Match patterns like "l6" or "l2" (should be "16" or "12")
            { regex: /([a-zA-Z])(\d+)/g, letterIndex: 1 },
            // Match patterns like "6x" or "4x" followed by a letter that should be a number
            { regex: /(\d+)\s*[xX]\s*([a-zA-Z])/g, letterIndex: match => match[0].lastIndexOf(match[2]) },
            // Match patterns like a letter followed by inches or feet symbol
            { regex: /([a-zA-Z])(["''])/g, letterIndex: 1 },
            // Match patterns like "l6xl2" (should be "16x12")
            { regex: /([a-zA-Z])(\d+)[xX]([a-zA-Z])(\d+)/g, letterIndices: [1, 3] }
        ];

        // Process dimension patterns first
        for (const pattern of dimensionPatterns) {
            if (pattern.letterIndices) {
                // Handle patterns with multiple letter positions
                result = result.replace(pattern.regex, (match, ...args) => {
                    let newMatch = match;
                    for (const letterIdx of pattern.letterIndices) {
                        const letter = args[letterIdx - 1];
                        if (alphabetToNumber[letter]) {
                            newMatch = newMatch.replace(letter, alphabetToNumber[letter]);
                        }
                    }
                    return newMatch;
                });
            } else {
                // Process each match
                const matches = [...result.matchAll(pattern.regex)];
                for (const match of matches) {
                    const letterIndex = typeof pattern.letterIndex === 'function' ?
                        match.index + pattern.letterIndex(match) :
                        match.index + pattern.letterIndex - 1;

                    if (letterIndex >= 0 && letterIndex < result.length) {
                        const letter = result[letterIndex];
                        const number = alphabetToNumber[letter];

                        if (number) {
                            result = result.substring(0, letterIndex) + number + result.substring(letterIndex + 1);
                        }
                    }
                }
            }
        }

        // Then process general patterns
        for (const [letter, number] of Object.entries(alphabetToNumber)) {
            // Define all patterns to check
            const patterns = [
                // Letter followed by quotation mark
                { regex: new RegExp(`${letter}["']`, 'gi'), letterPos: 0, checkBefore: true, checkAfter: true, afterPos: 1 },
                // Letter before slash
                { regex: new RegExp(`${letter}/`, 'gi'), letterPos: 0, checkBefore: true, checkAfter: false },
                // Letter after slash
                { regex: new RegExp(`/${letter}`, 'gi'), letterPos: 1, checkBefore: false, checkAfter: true },
                // Letter before x
                { regex: new RegExp(`${letter}\\s*[xX]`, 'gi'), letterPos: 0, checkBefore: true, checkAfter: false },
                // Letter after x
                { regex: new RegExp(`[xX]\\s*${letter}`, 'gi'), letterPos: match => match[0].indexOf(letter), checkBefore: false, checkAfter: true }
            ];

            // Process each pattern
            for (const pattern of patterns) {
                const matches = [...result.matchAll(pattern.regex)];

                for (const match of matches) {
                    // Get the index of the letter in the match
                    const letterIndex = typeof pattern.letterPos === 'function' ?
                        match.index + pattern.letterPos(match) :
                        match.index + pattern.letterPos;

                    let shouldReplace = true;

                    // Check if not preceded by a different letter
                    if (pattern.checkBefore && letterIndex > 0) {
                        const isPrecededByDifferentLetter = /[a-zA-Z]/i.test(result[letterIndex-1]) &&
                            result[letterIndex-1].toLowerCase() !== letter.toLowerCase();

                        if (isPrecededByDifferentLetter) {
                            shouldReplace = false;
                        }
                    }

                    // Check if not followed by a different letter
                    if (pattern.checkAfter && shouldReplace) {
                        const checkIndex = pattern.afterPos !== undefined ?
                            letterIndex + pattern.afterPos :
                            letterIndex + 1;

                        if (checkIndex < result.length) {
                            const isFollowedByDifferentLetter = /[a-zA-Z]/i.test(result[checkIndex]) &&
                                result[checkIndex].toLowerCase() !== letter.toLowerCase();

                            if (isFollowedByDifferentLetter) {
                                shouldReplace = false;
                            }
                        }
                    }

                    // Only replace if not joined with different alphabets
                    if (shouldReplace) {
                        result = result.substring(0, letterIndex) + number + result.substring(letterIndex + 1);
                    }
                }
            }
        }

        // Final pass to catch any remaining common patterns
        // Handle cases where numbers are adjacent to dimension indicators
        result = result.replace(/(\d+)([xX])([a-zA-Z])(\d+)/g, (match, num1, x, letter, num2) => {
            const number = alphabetToNumber[letter];
            return number ? `${num1}${x}${number}${num2}` : match;
        });

        // Handle fractions with letter substitutions
        result = result.replace(/(\d+)-([a-zA-Z])\/(\d+)/g, (match, whole, letter, denom) => {
            const number = alphabetToNumber[letter];
            return number ? `${whole}-${number}/${denom}` : match;
        });

        // Restore protected sections
        for (const [token, originalText] of protectedSections.entries()) {
            result = result.replace(token, originalText);
        }

        return result;
    }

    async extractMeasurements(text) {
        // Normalize text for consistent processing
        text = text.toLowerCase().trim();

        // Define patterns in order of specificity (most specific first)
        const patterns = [
            // Pattern for 3 dimensions with quotes (e.g., 4" x 2" x 3/16")
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?/i,
                process: (match) => [match[1], match[2], match[3]]
            },

            // Pattern for 2 dimensions with quotes (e.g., 4" x 2")
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?/i,
                process: (match) => [match[1], match[2]]
            },

            // Pattern for gauge notation (e.g., 16 ga x 48 x 96)
            {
                regex: /(\d+)\s*ga(?:uge)?\.?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1] + " ga", match[2], match[3]]
            },

            // Pattern for diameter notation (e.g., Ø2" or dia 2")
            {
                regex: /(?:ø|dia(?:meter)?\.?)\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?/i,
                process: (match) => [match[1]]
            },

            // Pattern for wall thickness notation (e.g., 2 x 2 x 0.25 wall)
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*wall/i,
                process: (match) => [match[1], match[2], match[3]]
            },

            // Pattern for dimensions with no separators (e.g., 4x3)
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1], match[2]]
            },

            // Pattern for angle dimensions (e.g., L3x3x1/4)
            {
                regex: /l(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1], match[2], match[3]]
            },

            // Pattern for HSS/tube dimensions (e.g., HSS4x4x1/4)
            {
                regex: /(?:hss|ts)(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1], match[2], match[3]]
            },

            // Pattern for channel dimensions (e.g., C10x15.3)
            {
                regex: /[c](\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1], match[2]]
            },

            // Pattern for beam dimensions (e.g., W8x31)
            {
                regex: /[w](\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/i,
                process: (match) => [match[1], match[2]]
            },

            // Pattern for plate dimensions (e.g., PL1/4x12x24)
            {
                regex: /pl(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)(?:x(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?))?/i,
                process: (match) => match[3] ? [match[1], match[2], match[3]] : [match[1], match[2]]
            },

            // Pattern for pipe dimensions (e.g., 2" SCH 40)
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)\s*["']?\s*(?:sch|schedule)\s*(\d+)/i,
                process: (match) => [match[1], `SCH ${match[2]}`]
            },

            // Fallback pattern for any dimension-like format
            {
                regex: /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?(?:\s*["'])?(?:\s*[xX]\s*\d+(?:-\d+\/\d+|\.\d+|\/\d+)?(?:\s*["'])?)*)(?:\s*[xX]\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?))(?:\s*["'])?/i,
                process: (match) => {
                    // Extract all dimensions from the matched string
                    const dimensions = match[0].split(/\s*[xX]\s*/);
                    return dimensions.map(dim => dim.replace(/["']/g, '').trim());
                }
            }
        ];

        // Try each pattern in order
        for (const pattern of patterns) {
            const match = text.match(pattern.regex);
            if (match) {
                const dimensions = pattern.process(match);
                // Validate dimensions (ensure they're not empty and look like measurements)
                const validDimensions = dimensions.filter(dim =>
                    dim && /\d/.test(dim) && !/[a-zA-Z]/.test(dim.replace(/ga(?:uge)?|sch(?:edule)?/i, ''))
                );

                if (validDimensions.length > 0) {
                    // Return the original match and the processed dimensions
                    return [match[0], ...validDimensions];
                }
            }
        }

        // If no pattern matched, try the original regex as a fallback
        const measurementRegex = /(\d+(?:-\d+\/\d+)|(?:\d+(?:\.\d+)?|\.\d+)\/(?:\d+(?:\.\d+)?|\.\d+)|(?:\d+(?:\.\d+)?|\.\d+))(?:\s*["']?\s*[xX]\s*(\d+(?:-\d+\/\d+)|(?:\d+(?:\.\d+)?|\.\d+)\/(?:\d+(?:\.\d+)?|\.\d+)|(?:\d+(?:\.\d+)?|\.\d+)))?(?:\s*["']?\s*[xX]\s*(\d+(?:-\d+\/\d+)|(?:\d+(?:\.\d+)?|\.\d+)\/(?:\d+(?:\.\d+)?|\.\d+)|(?:\d+(?:\.\d+)?|\.\d+)))?/i;

        const fallbackMatch = text.match(measurementRegex);
        if (fallbackMatch) {
            return fallbackMatch;
        }

        // Last resort: try to find any numbers that might be dimensions
        const numberPattern = /(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)/g;
        const numbers = [...text.matchAll(numberPattern)];
        if (numbers.length > 0) {
            // Only use numbers that are likely to be dimensions (not dates, quantities, etc.)
            const potentialDimensions = numbers
                .map(m => m[1])
                .filter(n => !n.match(/^(19|20)\d{2}$/)); // Filter out years

            if (potentialDimensions.length > 0) {
                return [text, ...potentialDimensions.slice(0, 3)]; // Return up to 3 dimensions
            }
        }

        return null;
    }

    async extractSpecAndGrade(text) {
        let spec = null;
        let grade = null;

        // Normalize text for consistent processing
        text = text.toLowerCase().trim();

        // Define specification patterns with word boundaries for more accurate matching
        const specPatterns = [
           // A572 Grade 50
            {
                regex: /\b(?:a|astm\s+a)(?:572(?:-|\s+gr(?:ade)?\s+)?50|57250)\b/i,
                spec: "A572",
                grade: "50"
            },

            // A500 Grade B
            {
                regex: /\b(?:a|astm\s+a)(?:500(?:-|\s+gr(?:ade)?\s+)?b|500b)\b/i,
                spec: "A500",
                grade: "B"
            },

            // A500 Grade C
            {
                regex: /\b(?:a|astm\s+a)(?:500(?:-|\s+gr(?:ade)?\s+)?c|500c)\b/i,
                spec: "A500",
                grade: "C"
            },

            // A500 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:500|-500)\b/i,
                spec: "A500"
            },

            // A53 Grade B
            {
                regex: /\b(?:a|astm\s+a)(?:53(?:-|\s+gr(?:ade)?\s+)?b|53b)\b/i,
                spec: "A53",
                grade: "B"
            },

            // A53 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:53)\b/i,
                spec: "A53"
            },

            // A106 Grade B
            {
                regex: /\b(?:a|astm\s+a)(?:106(?:-|\s+gr(?:ade)?\s+)?b|106b)\b/i,
                spec: "A106",
                grade: "B"
            },

            // A992 Grade 50
            {
                regex: /\b(?:a|astm\s+a)(?:992(?:-|\s+gr(?:ade)?\s+)?50|99250)\b/i,
                spec: "A992",
                grade: "50"
            },

            // A992 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:992)\b/i,
                spec: "A992"
            },

            // A36 which is very common in carbon steel
            {
                regex: /\b(?:a|astm\s+a)(?:36|-36)\b/i,
                spec: "A36"
            },

            // A193 Grade B7
            {
                regex: /\b(?:a|astm\s+a)(?:193(?:-|\s+gr(?:ade)?\s+)?b7|193b7)\b|\bb7\b/i,
                spec: "A193",
                grade: "B7"
            },

            // A572 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:572)\b/i,
                spec: "A572"
            },

            // A513
            {
                regex: /\b(?:a|astm\s+a)(?:513)\b/i,
                spec: "A513"
            },

            // A1011
            {
                regex: /\b(?:a|astm\s+a)(?:1011)\b/i,
                spec: "A1011"
            },

            // A1008
            {
                regex: /\b(?:a|astm\s+a)(?:1008)\b/i,
                spec: "A1008"
            },

            // A786
            {
                regex: /\b(?:a|astm\s+a)(?:786)\b/i,
                spec: "A786"
            },

            // A529
            {
                regex: /\b(?:a|astm\s+a)(?:529)\b/i,
                spec: "A529"
            },

            // A656
            {
                regex: /\b(?:a|astm\s+a)(?:656)\b/i,
                spec: "A656"
            },

            // A283 Grade C/D
            {
                regex: /\b(?:a|astm\s+a)(?:283(?:-|\s+gr(?:ade)?\s+)?([cd]))\b/i,
                spec: "A283",
                gradeFromMatch: (match) => match[1]?.toUpperCase()
            },

            // A283 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:283)\b/i,
                spec: "A283"
            },

            // A516 Grade 70
            {
                regex: /\b(?:a|astm\s+a)(?:516(?:-|\s+gr(?:ade)?\s+)?(\d+))\b/i,
                spec: "A516",
                gradeFromMatch: (match) => match[1]
            },

            // A516 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:516)\b/i,
                spec: "A516"
            },

            // A709 Grade 36/50/70
            {
                regex: /\b(?:a|astm\s+a)(?:709(?:-|\s+gr(?:ade)?\s+)?(\d+))\b/i,
                spec: "A709",
                gradeFromMatch: (match) => match[1]
            },

            // A709 without grade
            {
                regex: /\b(?:a|astm\s+a)(?:709)\b/i,
                spec: "A709"
            },

            // A325 high-strength bolts
            {
                regex: /\b(?:a|astm\s+a)(?:325)\b/i,
                spec: "A325"
            },

            // A490 high-strength bolts
            {
                regex: /\b(?:a|astm\s+a)(?:490)\b/i,
                spec: "A490"
            },
            
            // Combined specifications
            {
                regex: /\b(?:a|astm\s+a)(?:36|36\/a572(?:-|\s+gr(?:ade)?\s+)?50|36\/a572-50)\b/i,
                spec: "A36/A572",
                grade: "50"
            },
        ];

        // Try each pattern in order
        for (const pattern of specPatterns) {
            const match = text.match(pattern.regex);
            if (match) {
                spec = pattern.spec;

                // Set grade either from the pattern or from the match
                if (pattern.grade) {
                    grade = pattern.grade;
                } else if (pattern.gradeFromMatch) {
                    grade = pattern.gradeFromMatch(match);
                }

                // For high-priority matches, return immediately
                if (pattern.highPriority) {
                    return [spec, grade];
                }

                break; // Stop after first match
            }
        }
        // If no spec was found, try to extract grade separately
        if (!grade && spec) {
            // Look for grade patterns
            const gradePatterns = [
                // Grade followed by a number (e.g., "Grade 50", "Gr 50", "GR50")
                { regex: /\bgr(?:ade)?\s*(\d+)\b/i, process: (match) => match[1] },

                // Grade followed by a letter (e.g., "Grade B", "Gr B", "GRB")
                { regex: /\bgr(?:ade)?\s*([a-z])\b/i, process: (match) => match[1].toUpperCase() },

                // KSI notation (e.g., "50 ksi")
                { regex: /\b(\d+)\s*ksi\b/i, process: (match) => match[1] }
            ];

            for (const pattern of gradePatterns) {
                const match = text.match(pattern.regex);
                if (match) {
                    grade = pattern.process(match);
                    break;
                }
            }
        }

        return [spec, grade];
    }

    async extractLength(length, shapeType, dimensionsLength) {
        let extractLength = null;
        if ((extractLength = length.match(/(\d+)\s*ft/))) { // Check for 'ft' first
            let length = parseInt(extractLength[1], 10) * 12; // converting ft into inches
            return length;
        } else if ((extractLength = length.match(/(\d+)\s*'/))) { // Check for single quote (')
            let length = parseInt(extractLength[1], 10) * 12; // converting ft into inches
            return length;
        } else if ((extractLength = length.match(/^(\d+)-\d+$/))) {  // check for format 20-10 here 20 is ft
            let length = parseInt(extractLength[1], 10) * 12; // convert ft into inches
            return length
        }else if ((extractLength = length.match(/^(\d+)-\d+"/))){
            let length = parseInt(extractLength[1], 10) * 12; // convert ft into inches
            return length;
        } else { // Check for double quote (") - find last occurrence
            const lastDoubleQuoteIndex = length.lastIndexOf('"');
            if (lastDoubleQuoteIndex !== -1) {
                const beforeLastQuote = length.substring(0, lastDoubleQuoteIndex);
                let lengthInInches = beforeLastQuote.match(/(\d+)\s*$/);
                if (lengthInInches) {
                    return parseInt(lengthInInches[1], 10);
                }
            }
        }

        // First check for explicit unit indicators
        const match = length.match(/(\d{1,3})\s*(ft|feet|'|in|inches|"|drl|srl)/i);
        if (match) {
            const [, val, unit] = match;
            const unitLower = unit.toLowerCase();
            if (unitLower.includes("ft") || unitLower.includes("'") || unitLower.includes("drl") || unitLower.includes("srl")) {
                return parseInt(val, 10) * 12;
            } else if ( (unitLower.includes("in") || unitLower.includes('"') ) && dimensionsLength > 2 ) {
                return parseInt(val, 10);
            }
        }

        // Check for hyphenated values like "20-3" where first number is feet and second is inches
        const hyphenMatch = length.match(/(\d{1,3})-(\d{1,2})/);
        if (hyphenMatch) {
            const [, feet] = hyphenMatch;
            return `${feet} FT`;
        }

        // shapes that don't have length in FT
        if(shapeType){
            const shapesWithoutLength = ["coil", "plate", "sheet"];
            if(!shapesWithoutLength.includes(shapeType.toLowerCase()) && Number(length)){
                return `${length} FT`;
            }
        }

        return null;
    }

    async setDimensionsByShape(shapeType, raw){
        let dimensions = null;

        // Normalize raw text for consistent processing
        raw = raw.toLowerCase().trim()
            .replace(/(\d+)\s*'/g, "") // Remove numbers along with single quotes
            .replace(/(\d+)'/g, ""); // Remove numbers along with single quotes (no space)

        // Define improved regex patterns for each shape type
        const shapePatterns = {
            // Angle pattern: matches formats like "3 x 3 x 1/4", "L3x3x1/4", "3" x 3" x 1/4""
            angle: /(?:l\s*)?(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?/i,

            // Beam pattern: matches formats like "W8 x 31", "8 x 31", "W8x31"
            beam: /(?:w\s*)?(\d+(?:\.\d+)?)\s*["']?\s*[xX]\s*(\d+(?:\.\d+)?)\s*["']?/i,

            // Channel pattern: improved to handle "C10 x 15.3", "MC8 x 21.4", "C10x15.3"
            channel: /(?:(mc|c)\s*)?(\d+(?:\.\d+)?)\s*["']?\s*[xX]\s*(\d+(?:\.\d+)?)\s*["']?(?:\s*x\s*(\d+(?:\.\d+)?)\s*["']?)?/i,

            // Plate pattern: improved to handle various plate formats
            plate: /(?:pl\s*)?(\d+(?:-\d+\/\d+|\.\d+|\/\d+(?:\/\d+)?)?|\d+\s*ga|gal|ga.|galv\.?)(?:\s+(?:galv\.?|galvanized|gal\.?))?(?:\s+(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?)?)?(?:\s*[\"']*)(?:\s+|(?:\s*[xX]\s*))(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?|\d+)(?:\s*[\"']*)(?:\s*[xX]\s*)?(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?|\d+)?(?:\s*[\"']*)?/i,

            // HSS pattern: improved to handle "HSS4x4x1/4", "TS4x4x1/4", "4 x 4 x 1/4 HSS"
            hss: /(?:(?:hss|ts)\s*)?(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?\s*x\s*(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?\s*(?:x\s*(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?)?(?:\s*(?:hss|ts))?/i,

            // Bar pattern: improved to handle "1/2 x 4", "FB1/2x4", "1/2" x 4""
            bar: /(?:(?:fb|rb|sb)\s*)?(?:(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?|\d+)\s*["']?\s*)?x\s*(\d+(?:-\d+)?\/\d+|\d+(?:\.\d+)?)\s*["']?/i,

            // Sheet pattern: improved to handle "16 ga x 48 x 96", "16GA x 48 x 96"
            sheet: /(\d+(?:\s*ga\.?)?|\d+\/\d+)\s*["']?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?|\d+)(?:\s*["']?)\s*(?:x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?|\d+)(?:\s*["']?))?/i,

            // Coil pattern: improved to handle various coil formats like "16 ga x 48", "0.075 x 48 coil"
            coil: /(\d+(?:\s*ga\.?)?|\d+\/\d+|\d+\.\d+)\s*["']?\s*x\s*(\d+(?:-\d+\/\d+|\.\d+|\/\d+)?|\d+)(?:\s*["']?)\s*(?:coil)?/i,

            // Pipe patterns are handled separately below
        };

        const fetchLBRegex = /(\d+(?:\.\d+)?)\s*(?:LB|lb|lbs|#)/i;

        // Special handling for pipe shapes
        let pattern = shapePatterns[shapeType.toLowerCase()];
        let pipeCategory = null;

        if(shapeType.toLowerCase() === "pipe") {
            // Check for different pipe types in order of specificity

            // Check for schedule pipes (e.g., "2" SCH 40", "2 inch schedule 40")
            if(raw.includes("sch") || raw.includes("schedule")) {
                const scheduleMatch = raw.match(/sch(?:edule)?\.?\s*(\d+)/i);
                if (scheduleMatch) {
                    const scheduleNumber = scheduleMatch[1];

                    // Find the pipe size
                    const pipeSizeRegex = /(?:^|\s)((?:\d+(?:-\d+)?\/\d+|\d+\/\d+|\d+(?:\.\d+)?|\d+))\s*["']?/i;
                    const pipeSizeMatch = raw.match(pipeSizeRegex);

                    if (pipeSizeMatch) {
                        dimensions = `${pipeSizeMatch[1]}"`;
                        pipeCategory = `SCH ${scheduleNumber}`;
                        return [dimensions, pipeCategory];
                    }
                }

                // Fallback pattern for schedule pipes
                pattern = /(\d+(?:-\d+\/\d+)?|\d+\/\d+|\d+)\s*["']?.*?sch(?:edule)?\.?\s*(\d+)/i;
                shapeType = "pipe-sch";
            }
            // Check for standard pipes (e.g., "2" STD", "2 inch standard")
            else if(raw.includes("std") || raw.includes("standard") ||
                    raw.includes("s1e") || raw.includes("s2e")) {

                // Extract the pipe size
                const pipeSizeRegex = /(?:^|\s)((?:\d+(?:-\d+)?\/\d+|\d+\/\d+|\d+(?:\.\d+)?|\d+))\s*["']?/i;
                const pipeSizeMatch = raw.match(pipeSizeRegex);

                if (pipeSizeMatch) {
                    dimensions = `${pipeSizeMatch[1]}"`;

                    // Determine the specific standard type
                    if (raw.includes("s1e")) {
                        pipeCategory = "S1E";
                    } else if (raw.includes("s2e")) {
                        pipeCategory = "S2E";
                    } else {
                        pipeCategory = "STD";
                    }

                    return [dimensions, pipeCategory];
                }

                // Fallback pattern for standard pipes
                pattern = /(\d+(?:-\d+\/\d+)?|\d+\/\d+|\d+)\s*["']?\s*(?:std|standard|s1e|s2e)/i;
                shapeType = "pipe-std";
            }
            // Default pipe pattern for simple pipe dimensions (e.g., "2"", "2 inch")
            else {
                pattern = /(\d+(?:-\d+\/\d+)?|\d+\/\d+|\d+(?:\.\d+)?)\s*["']/i;
            }
        }

        // Try to match the pattern
        let match = raw.match(pattern);

        // If no match with the shape-specific pattern, try extractMeasurements
        if(!match) {
            match = await this.extractMeasurements(raw);
        }

        let category = pipeCategory; // Use pipe category if already determined

        if (match) {
            const dims = match.slice(1);
            // Filter out non-numeric dimensions and shape prefixes
            const dimsClean = dims.filter(d => d && !["mc", "c", "hss", "ts", "w", "l", "pl", "fb", "rb", "sb"].includes(d.toLowerCase()) &&
                                         !/[a-zA-Z]/.test(d.replace(/ga(?:uge)?|sch(?:edule)?|std|s[12]e/i, '')));

            // Process dimensions based on shape type
            switch (shapeType.toLowerCase()) {
                case "beam": {
                    // For beams, format is typically height x weight
                    const [h, w] = dimsClean;
                    if(!w) {
                        // If weight is not in dimensions, try to find it in the text
                        const fetchLB = raw.match(fetchLBRegex);
                        if(fetchLB) {
                            dimensions = `${h}x${fetchLB[1]}`;
                        } else {
                            dimensions = `${h}`;
                        }
                    } else {
                        dimensions = `${h}x${w}`;
                    }

                    break;
                }
                case "angle": {
                    // For angles, format is typically leg x leg x thickness
                    if (dimsClean.length >= 3) {
                        const [a, b, t] = dimsClean;
                        dimensions = `${a}x${b}x${t}`;
                    } else if (dimsClean.length === 2) {
                        const [a, b] = dimsClean;
                        dimensions = `${a}x${b}`;

                        // Try to find thickness in the raw text
                        const thicknessMatch = raw.match(/(?:x|by)\s*(\d+\/\d+|\d+\.\d+|\d+)\s*["']?/i);
                        if (thicknessMatch && thicknessMatch[1]) {
                            dimensions += `x${thicknessMatch[1]}`;
                        }
                    }

                    // Check if it's an equal or unequal angle
                    if (dimsClean.length >= 2) {
                        if (dimsClean[0] === dimsClean[1]) {
                            category = category || "Equal";
                        } else {
                            category = category || "Unequal";
                        }
                    }

                    break;
                }
                case "plate": {
                    // For plates, handle various dimension formats
                    if (dimsClean.length === 4) {
                        // Handle case with both thickness parts (e.g., "3/8 3/8 x 10-1/4 x 12")
                        const [t1, t2, w, l] = dimsClean;
                        dimensions = `${t1} ${t2}x${w}x${l}`;
                    } else if (dimsClean.length === 3) {
                        const [t, w, l] = dimsClean;
                        dimensions = `${t}x${w}x${l}`;
                    } else if (dimsClean.length === 2) {
                        const [t, w] = dimsClean;
                        dimensions = `${t}x${w}`;
                    } else if (dimsClean.length === 1) {
                        const [t] = dimsClean;
                        dimensions = `${t}`;
                    }

                    // Check for plate type
                    if (raw.includes("checker") || raw.includes("chkr")) {
                        category = category || "Checker";
                    } else if (raw.includes("diamond") || raw.includes("floor")) {
                        category = category || "Floor";
                    } else if (raw.includes("galv") || raw.includes("galvanized")) {
                        category = category || "Galvanized";
                    }

                    break;
                }
                case "pipe-sch": {
                    // For schedule pipes, format is diameter + schedule
                    const [od, sched] = dimsClean;
                    dimensions = `${od}"`;
                    category = category || `SCH ${sched}`;
                    break;
                }
                case "pipe-std": {
                    // For standard pipes, format is diameter + STD
                    const [od] = dimsClean;
                    dimensions = `${od}"`;
                    category = category || 'STD';
                    break;
                }
                case "pipe": {
                    // For generic pipes, just use the diameter
                    if (dimsClean.length > 0) {
                        const [dim] = dimsClean;
                        dimensions = `${dim}"`;
                    }

                    // Check for pipe type in raw text
                    if (raw.includes("seamless")) {
                        category = category || "Seamless";
                    } else if (raw.includes("welded")) {
                        category = category || "Welded";
                    }

                    break;
                }
                case "hss": {
                    // For HSS/tubes, format is width x height x wall thickness
                    if (dimsClean.length >= 3) {
                        const [w, h, wall] = dimsClean;
                        dimensions = `${w}x${h}x${wall}`;
                    } else if (dimsClean.length === 2) {
                        const [w, h] = dimsClean;
                        dimensions = `${w}x${h}`;

                        // Try to find wall thickness in the raw text
                        const wallMatch = raw.match(/(?:x|by)\s*(\d+\/\d+|\d+\.\d+|\d+)\s*(?:wall|thk|thick)/i);
                        if (wallMatch && wallMatch[1]) {
                            dimensions += `x${wallMatch[1]}`;
                        }
                    } else if (dimsClean.length === 1) {
                        dimensions = `${dimsClean[0]}`;
                    }

                    // Determine if it's square or rec
                    if (dimsClean.length >= 2) {
                        if (dimsClean[0] === dimsClean[1]) {
                            category = category || "Square";
                        } else {
                            category = category || "Rec";
                        }
                    } else if (raw.includes("square") || raw.includes("sq")) {
                        category = category || "Square";
                    } else if (raw.includes("rect") || raw.includes("rec")) {
                        category = category || "Rec";
                    } else if (raw.includes("round") || raw.includes("rnd")) {
                        category = category || "Round";
                    }

                    break;
                }
                case "bar": {
                    // For bars, format is thickness x width
                    if (dimsClean.length >= 2) {
                        const [t, w] = dimsClean;
                        dimensions = `${t}x${w}`;
                    } else if (dimsClean.length === 1) {
                        const [t] = dimsClean;
                        dimensions = `${t}`;

                        // Try to find width in the raw text
                        const widthMatch = raw.match(/(?:x|by)\s*(\d+\/\d+|\d+\.\d+|\d+)/i);
                        if (widthMatch && widthMatch[1]) {
                            dimensions += `x${widthMatch[1]}`;
                        }
                    }

                    // Determine bar type
                    if (raw.includes("flat") || raw.includes("fb")) {
                        category = category || "fb";
                    } else if (raw.includes("round") || raw.includes("rb")) {
                        category = category || "rb";
                    } else if (raw.includes("square") || raw.includes("sb")) {
                        category = category || "sb";
                    }

                    break;
                }
                case "sheet": {
                    // For sheets, format is gauge/thickness x width x length
                    if (dimsClean.length >= 3) {
                        const [t, w, l] = dimsClean;
                        dimensions = `${t}x${w}x${l}`;
                    } else if (dimsClean.length === 2) {
                        const [t, w] = dimsClean;
                        dimensions = `${t}x${w}`;
                    } else if (dimsClean.length === 1) {
                        dimensions = `${dimsClean[0]}`;
                    }

                    // Check for sheet type
                    if (raw.includes("galv") || raw.includes("galvanized")) {
                        category = category || "Galvanized";
                    }

                    break;
                }
                case "coil": {
                    // For coils, format is typically gauge/thickness x width
                    if (dimsClean.length >= 2) {
                        const [t, w] = dimsClean;
                        dimensions = `${t}x${w}`;
                    } else if (dimsClean.length === 1) {
                        dimensions = `${dimsClean[0]}`;

                        // Try to find width in the raw text
                        const widthMatch = raw.match(/(?:x|by)\s*(\d+\/\d+|\d+\.\d+|\d+)/i);
                        if (widthMatch && widthMatch[1]) {
                            dimensions += `x${widthMatch[1]}`;
                        }
                    }

                    // Check for coil type
                    if (raw.includes("galv") || raw.includes("galvanized")) {
                        category = category || "Galvanized";
                    } else if (raw.includes("hr") || raw.includes("hot rolled")) {
                        category = category || "HR";
                    } else if (raw.includes("cr") || raw.includes("cold rolled")) {
                        category = category || "CR";
                    } else if (raw.includes("gi") || raw.includes("galv iron")) {
                        category = category || "GI";
                    } else if (raw.includes("pp") || raw.includes("prepaint")) {
                        category = category || "Prepainted";
                    }

                    break;
                }
                case "channel": {
                    // For channels, format is height x weight
                    if (dimsClean.length >= 2) {
                        const [d1, d2] = dimsClean;
                        dimensions = `${d1}x${d2}`;
                    } else if (dimsClean.length === 1) {
                        const [d1] = dimsClean;

                        // If weight is not in dimensions, try to find it in the text
                        const fetchLB = raw.match(fetchLBRegex);
                        if (fetchLB) {
                            dimensions = `${d1}x${fetchLB[1]}`;
                        } else {
                            dimensions = `${d1}`;
                        }
                    }

                    // Determine channel type
                    if (raw.includes("mc") || raw.includes("misc")) {
                        category = category || "MC";
                    } else if (raw.includes("c ") || raw.match(/\bc\d/i)) {
                        category = category || "C";
                    } else if (raw.includes("ship") || raw.includes("car")) {
                        category = category || "Ship";
                    } else if (raw.includes("struc") || raw.includes("structural")) {
                        category = category || "Structural";
                    }

                    break;
                }
            }
        }

        return [dimensions, category];
    }

    async tryParseShape(description, spec, grade, length, shapeType = null,category = null) {

        const processedRaw = await this.figureOutDimensionsFromAlphabets(description);

        const specAndGrade = spec || grade ?  await this.normalizeInput(spec+" "+grade) : processedRaw.toLowerCase(); // A36
        [spec, grade] = await this.extractSpecAndGrade(specAndGrade);

        let raw = await this.normalizeInput(processedRaw);
        if(raw.includes(shapeType)){
            raw = raw.replace(shapeType,shapeType+" ");
        }

        if(spec){
            raw = raw.replace(new RegExp(`\\b\\w*${spec.toLowerCase()}\\w*\\b`, 'g'), ""); // remove whole word where spec value is present from raw
        }

        raw = await this.normalizeInput(raw);
        let dimensions = null;
        let extractedLength = null;

        if(shapeType === 'sheet' && category === 'tread'){
            //For shape type sheet and category tread, we just need to set dimensions
            [dimensions] = await this.setDimensionsByShape(shapeType, raw);
        }else if(shapeType){
            [dimensions, category] = await this.setDimensionsByShape(shapeType, raw);
        }else{
            const dims = await this.extractMeasurements(raw);
            let measurements = [];
            if(dims){
                measurements = dims.slice(1).filter(dim => dim !== undefined);
            }
            if(measurements.length > 0){
                dimensions = "";
                for(const measurement of measurements){
                    dimensions += measurement + 'x';
                }
                dimensions = dimensions.slice(0, -1);
            }
        }

        length = await this.normalizeInput(length);
        const validDimensions = dimensions ? dimensions.split(" x ").length : 0;
        extractedLength = await this.extractLength(length, shapeType, validDimensions);

        // Process the raw description to convert alphabets to numbers if needed
        const result = {
            shape: shapeType ? shapeType.charAt(0).toUpperCase() + shapeType.slice(1) : null,
            dimensions: dimensions,
            length: extractedLength,
            spec: spec,
            grade: grade,
            category: category
        };
        return result;
    }
}