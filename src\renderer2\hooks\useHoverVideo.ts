import React from 'react';
import { useHoverVideoStore } from '../component/LeftPanel/HoverVideoStore';
import { useRightWindowStore } from '../pages/RightWindow/RightWindowStore';
import HoverVideoPlayer from '../component/HoverVideoPlayer/HoverVideoPlayer';
import { hoverVideoConfig } from '../hoverVideoCommon';

interface HoverVideoConfig {
  videoUrl: string;
  title: string;
  description: string;
}

export const useHoverVideo = () => {
  const { isHoverVideoEnabled, setVideoToolTipData } = useHoverVideoStore();
  const { setToolTipVideoComponent, toolTipVideoComponent } = useRightWindowStore();


  const handleHoverVideoMouseEnter = (videoId: string) => {
    const config = hoverVideoConfig[videoId];
    const { isHoverVideoEnabled } = useHoverVideoStore.getState();
    if(config && isHoverVideoEnabled){
      setVideoToolTipData(config);
    }
    if (isHoverVideoEnabled && config && !toolTipVideoComponent) {
      setToolTipVideoComponent(
        React.createElement(HoverVideoPlayer)
      );
    }
  };

  const handleHoverVideoMouseLeave = () => {
    setVideoToolTipData(null);
    setToolTipVideoComponent(null);

  };

  return {
    handleHoverVideoMouseEnter,
    handleHoverVideoMouseLeave,
    isHoverVideoEnabled
  };
}; 