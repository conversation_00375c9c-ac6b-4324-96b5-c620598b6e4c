<svg width="254" height="44" viewBox="0 0 254 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_717_6801)">
<rect x="2" y="2" width="250" height="40" rx="12" fill="white" fill-opacity="0.04"/>
<rect x="1.25" y="1.25" width="251.5" height="41.5" rx="12.75" stroke="url(#paint0_linear_717_6801)" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_i_717_6801" x="0.5" y="0.5" width="255" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_6801"/>
</filter>
<linearGradient id="paint0_linear_717_6801" x1="154.885" y1="117.588" x2="150.835" y2="11.9709" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C8B99"/>
<stop offset="1" stop-color="#2F2E33"/>
</linearGradient>
</defs>
</svg>
