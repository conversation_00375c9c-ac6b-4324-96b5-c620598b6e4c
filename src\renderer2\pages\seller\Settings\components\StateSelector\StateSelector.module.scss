.container {
  display: grid;
  grid-template-columns: repeat(17, 1fr);
  gap: 2px;
  padding: 4px;
  background-color: #2a2a2e;
  border-radius: 8px;
  width: 100%;
  max-width: 607.8px;
}

.stateItem {
  width: 33.2px;
  height: 24.1px;
  padding: 2px 2px;
  border-radius: 2.4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  font-family: Syncopate;
  font-size: 12.1px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.48px;
  text-align: left;
  color: #fff;
  transition: background-color 0.2s ease-in-out;
  border: 1px solid transparent;
  background: transparent;

  &:focus {
    background-color: #3e3e44;
  }

}

.stateItem:hover {
  background-color: #3e3e44;
}

.selected {
  background-color: #459fff !important;
  color: #ffffff !important;
}

.selected:hover {
  background-color: #459fff;
}