<svg width="294" height="164" viewBox="0 0 294 164" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_851_1751)">
<rect x="1" y="1" width="292" height="162" rx="13" fill="#23242A" fill-opacity="0.2"/>
</g>
<rect x="0.5" y="0.5" width="293" height="163" rx="13.5" stroke="url(#paint0_linear_851_1751)" stroke-opacity="0.33"/>
<defs>
<filter id="filter0_i_851_1751" x="0" y="0" width="298" height="168" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1751"/>
</filter>
<linearGradient id="paint0_linear_851_1751" x1="229.597" y1="196.652" x2="146.305" y2="-40.1114" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B20" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
