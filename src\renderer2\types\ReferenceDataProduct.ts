export type ReferenceDataProduct = {
    id: number;
    Shape_ID: number;
    Size_Group_ID: number;
    Product_ID: number;
    LBS_FT: string;
    Order_Increment_Ft: string;
    Order_Increment_Ea: string;
    Order_Increment_Lb: string;
    Order_Increment_CWT: string;
    Order_Increment_Net_Ton: string;
    Bucket: string;
    Key1: string;
    Key2: string;
    Key3: string;
    Key4: string;
    Key5: string;
    Key6: string;
    Key7: string;
    Key8: string;
    Key9: string;
    Key10: string;
    Key11: string;
    Key12: string;
    Key13: string;
    Key14: string;
    Key15: any;
    Key16: any;
    Key17: any;
    Key18: string;
    Key19: any;
    Key20: string;
    Key21: any;
    Key22: any;
    Key23: any;
    Key24: any;
    UI_Description: string;
    QUM_Dropdown_Options: string;
    PUM_Dropdown_Options: string;
    Neutral_Pricing_Ft: string;
    Neutral_Pricing_Ea: string;
    Neutral_Pricing_LB: string;
    Neutral_Pricing_CWT: string;
    Neutral_Pricing_Net_Ton: string;
    Actual_Neutral_Pricing_Ft: string;
    Actual_Neutral_Pricing_Ea: string;
    Actual_Neutral_Pricing_LB: string;
    Actual_Neutral_Pricing_CWT: string;
    Actual_Neutral_Pricing_Net_Ton: string;
    Buyer_Pricing_Ft: string;
    Buyer_Pricing_Ea: string;
    Buyer_Pricing_LB: string;
    Buyer_Pricing_CWT: string;
    Buyer_Pricing_Net_Ton: string;
    Actual_Buyer_Pricing_Ft: string;
    Actual_Buyer_Pricing_Ea: string;
    Actual_Buyer_Pricing_LB: string;
    Actual_Buyer_Pricing_CWT: string;
    Actual_Buyer_Pricing_Net_Ton: string;
    Seller_Pricing_Ft: string;
    Seller_Pricing_Ea: string;
    Seller_Pricing_LB: string;
    Seller_Pricing_CWT: string;
    Seller_Pricing_Net_Ton: string;
    Actual_Seller_Pricing_Ft: string;
    Actual_Seller_Pricing_Ea: string;
    Actual_Seller_Pricing_LB: string;
    Actual_Seller_Pricing_CWT: string;
    Actual_Seller_Pricing_Net_Ton: string;
    domestic_material_only: boolean;
    is_active: boolean;
    created_date: string;
    time_stamp: string;
    is_safe_product_code: boolean;
    reg0:number|null;
    reg1:number|null;
    reg2:number|null;
    reg3:number|null;
    reg4:number|null;
    reg5:number|null;
    reg6:number|null;
    reg7:number|null;
    reg8:number|null;
    reg9:number|null;
    qtyGrp0:number|null;
    qtyGrp1:number|null;
    qtyGrp2:number|null;
    qtyGrp3:number|null;
    qtyGrp4:number|null;
};
