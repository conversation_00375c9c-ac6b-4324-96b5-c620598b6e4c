import { bomLineStatusCountObjDefault } from 'src/renderer2/common';
import { create } from 'zustand';

const commonStore = {
    loadComponent:null,
    toolTipVideoComponent: null,
    showVideo: true,
    showBomProcessing: false,
    props: null,
    bomProcessingWindowProps: null,
    shareEmailWindowProps: null,
    bomLineStatusCountObj: {...bomLineStatusCountObjDefault},
    shareEmailType: null,
    scrollToBomLine: null,
    isCancelUpload: false,
    isPriceSearchHistory: false,
    isSharedPricingHistory: false,
    isSharedAppHistory: false,
}
  
interface RightWindowStore {
    loadComponent: React.ReactNode;
    toolTipVideoComponent: React.ReactNode;
    showVideo: boolean;
    showBomProcessing: boolean;
    props: any;
    setProps: (props: any) => void;
    bomProcessingWindowProps: any|null;
    setBomProcessingWindowProps: (bomProcessingWindowProps: any|null) => void;
    setLoadComponent: (loadComponent: React.ReactNode) => void;
    setToolTipVideoComponent: (toolTipVideoComponent: React.ReactNode) => void;
    setShowVideo: (showVideo: boolean) => void;
    setShowBomProcessing: (showBomProcessing: boolean) => void;
    resetRightWindowStore: () => void;
    setShareEmailWindowProps: (shareEmailWindowProps: any|null) => void;
    shareEmailWindowProps: any|null;
    bomLineStatusCountObj: Record<string, number>;
    setBOMLineStatusCountObj: (bomLineStatusCountObj: Record<string, number>) => void;
    shareEmailType: string|null;
    setShareEmailType: (shareEmailType: string|null) => void;
    scrollToBomLine: string|null;
    setScrollToBomLine: (scrollToBomLine: string|null) => void;
    isCancelUpload: boolean;
    setIsCancelUpload: (isCancelUpload: boolean) => void;
    isPriceSearchHistory: boolean;
    setIsPriceSearchHistory: (isPriceSearchHistory: boolean) => void;
    isSharedPricingHistory: boolean;
    setIsSharedPricingHistory: (isSharedPricingHistory: boolean) => void;
    isSharedAppHistory: boolean;
    setIsSharedAppHistory: (isSharedAppHistory: boolean) => void;
}
  
export const useRightWindowStore = create<RightWindowStore>((set, get) => ({
    ...commonStore,
    setLoadComponent: (loadComponent: React.ReactNode) => set({ loadComponent }),
    setToolTipVideoComponent: (toolTipVideoComponent: React.ReactNode) => set({ toolTipVideoComponent }),
    setProps: (props: any) => set({ props }),
    //setBomProcessingWindowProps: (bomProcessingWindowProps: any|null) => set({ bomProcessingWindowProps }),
    setBomProcessingWindowProps: (newProps: any|null) => set((state) => ({
        bomProcessingWindowProps: {
            ...(state.bomProcessingWindowProps ?? {}),
            ...(newProps ?? {}),
        }
    })),
    setShowBomProcessing: (showBomProcessing: boolean) => set({ showBomProcessing }),
    setShowVideo: (showVideo: boolean) => set({ showVideo }),
    setShareEmailWindowProps: (shareEmailWindowProps: any|null) => set({ shareEmailWindowProps }),
    setBOMLineStatusCountObj: (bomLineStatusCountObj: Record<string, number>) => set({ bomLineStatusCountObj }),
    setShareEmailType: (shareEmailType: string|null) => set({ shareEmailType }),
    setScrollToBomLine: (scrollToBomLine: string|null) => set({ scrollToBomLine }),
    setIsCancelUpload: (isCancelUpload: boolean) => set({ isCancelUpload }),
    setIsPriceSearchHistory: (isPriceSearchHistory: boolean) => set({ isPriceSearchHistory }),
    setIsSharedPricingHistory: (isSharedPricingHistory: boolean) => set({ isSharedPricingHistory }),
    setIsSharedAppHistory: (isSharedAppHistory: boolean) => set({ isSharedAppHistory }),
    resetRightWindowStore: () => set(() => ({
        ...commonStore
    })),
}));