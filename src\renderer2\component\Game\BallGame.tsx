import React, { useEffect, useRef, useState } from 'react';
import Game from './Game'; // Import the Game class from a separate file
import styles from './BallGame.module.scss';
import { ReactComponent as BigB } from "../../assets/New-images/Big-B-logo.svg";
import { ReactComponent as Ball } from "../../assets/New-images/BryzosBallLogo.svg";
import clsx from 'clsx';

const GameCanvas: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const bCharacterRef = useRef<HTMLDivElement | null>(null);
  const processingMsgRef = useRef<HTMLDivElement | null>(null);
  const speedBoostMsgRef = useRef<HTMLDivElement | null>(null);
  const scoreRef = useRef<HTMLSpanElement | null>(null);
  const livesRef = useRef<HTMLSpanElement | null>(null); 
  const gameInstanceRef = useRef<Game | null>(null);
  const [gameStarted, setGameStarted] = useState(false);
  const [hideAnimationContainer, setHideAnimationContainer] = useState(false);
  const [score, setScore] = useState(0);
  const [previousScore, setPreviousScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);

  const handleGameOver = ()=>{
    setGameOver(true)
    setGameStarted(false)
    setPreviousScore(score)
    setScore(0)
    setHideAnimationContainer(false)
  }

  useEffect(() => {
    // Create mutation observer
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' || mutation.type === 'characterData') {
          console.log('bCharacter content changed:', bCharacterRef.current?.textContent);
          if (bCharacterRef.current?.textContent === "gameOver"){
            handleGameOver()
            bCharacterRef.current.textContent =""
          }
          // Handle the content change here
        }
      });
    });

    // Start observing if ref is available
    if (bCharacterRef.current) {
      observer.observe(bCharacterRef.current, {
        childList: true,     // observe direct children
        characterData: true, // observe text content changes
        subtree: true       // observe all descendants
      });
    }

    // Cleanup observer on component unmount
    return () => {
      observer.disconnect();
    };
  }, []); 

  useEffect(() => {
    if (
      !canvasRef.current ||
      !bCharacterRef.current ||
      !processingMsgRef.current ||
      !speedBoostMsgRef.current ||
      !scoreRef.current ||
      !livesRef.current
    ) {
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const bCharacter = bCharacterRef.current;
    const processingMessage = processingMsgRef.current;
    const speedBoostMessage = speedBoostMsgRef.current;
    const scoreDisplay = scoreRef.current;
    const livesDisplay = livesRef.current;

    if (!ctx) return;

    const game = new Game(
      canvas,
      ctx,
      bCharacter,
      processingMessage,
      speedBoostMessage,
      scoreDisplay,
      livesDisplay,
      handleGameOver

    );
    gameInstanceRef.current = game;

    window.addEventListener('resize', () => game.resizeCanvas());
    window.addEventListener('keydown', (e) => game.handleKeyPress(e));

    return () => {
      window.removeEventListener('resize', () => game.resizeCanvas());
      window.removeEventListener('keydown', (e) => game.handleKeyPress(e));
      gameInstanceRef.current = null;
    };
  }, []);

  const startGame = (e:React.MouseEvent)=>{
    if(gameInstanceRef.current){
      setGameStarted(true);
      setGameOver(false)
      // setHideText(true);
      setTimeout(()=>{
        setHideAnimationContainer(true);
      },2000)
      setTimeout(()=>{
        gameInstanceRef.current.handleDoubleClick(e)
      },2500)
    }
  }

  return (
    <div style={{ position: 'relative', width: '100%', height: '880px' }}>
      {!hideAnimationContainer && <div className={clsx(styles.gameContainer)}>
        <div className={clsx(styles.bryzosBall, gameStarted && (styles.rotateAnimation) )}>
        <div className={styles.centerOverLay} onDoubleClick={startGame}/>
          <Ball />
        </div>
        <div  className={clsx(styles.BryzosB, gameStarted && styles.rotateAnimation2)}>
          <div className={styles.gameIcon}> <img src={'/asset/basketWhite.png'} alt="gameIcon" /></div>
          <div className={styles.initialIcon}> <BigB /></div>
        </div>
      </div>}
      <canvas ref={canvasRef} id="gameCanvas" style={{ display: gameStarted ? 'block' : 'none',  width:800, height:880}} />
      {!gameStarted && !gameOver && <div className={styles.initialPage}>
        <span className={styles.textTop}>HOW ABOUT A GAME OF <br />
          B-BALL WHILE YOU WAIT ?</span>
        
        <span className={styles.imgCenter} onDoubleClick={startGame}></span>
        <span className={styles.textBottom}>SCORE TO BEAT: <br />
          86 CONSECUTIVE CATCHES</span>
        <button onClick={startGame}>START</button>
      </div>}
      {!gameStarted && gameOver && <div className={styles.initialPage}>
        <span className={styles.textTop}>GAME OVER </span>
        
        <span className={styles.imgCenter} onDoubleClick={startGame}></span>
        <span className={styles.textBottom}>SCORE TO BEAT: <br />
          86 CONSECUTIVE CATCHES</span>
        <button onClick={startGame}>START</button>
      </div>}
      <span
        ref={bCharacterRef}
        id="b-character"
        onClick={()=>{console.log('clicked')}}
        style={{ position: 'absolute', cursor: 'pointer' , width:50}}
      >
      </span>
      <span ref={processingMsgRef} id="processing-message" className="message">
      </span>
      <span
        ref={speedBoostMsgRef}
        id="speed-boost-message"
        style={{ position: 'absolute', opacity: 0 }}
      >
      </span>
      <div className={styles.score}>
        Score: <span ref={scoreRef} id="scoreValue">0</span>
      </div>
      <div className={styles.live}>Lives:{' '}
        <span ref={livesRef} id="livesValue">3</span>
      </div>
    </div>
  );
};

export default GameCanvas;
