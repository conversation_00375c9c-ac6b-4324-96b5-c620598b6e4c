import { RESIZE_BOX, ZOOM, ADD_BOX, DELETE_BOX, ROTATE , useBomPdfExtractorStore } from '../BomPdfExtractorStore';


export const createActions = (action, beforeState, afterState) => {
    const { setUndoStack, setRedoStack } = useBomPdfExtractorStore.getState();
    const undoAction = { state:beforeState};
    const redoAction = { action:action, state:afterState};
    switch(action){
        case ADD_BOX:
            undoAction.action = DELETE_BOX;
        break;
        case DELETE_BOX:
            undoAction.action = ADD_BOX;
        break;
        //No need to change action for below actions
        case RESIZE_BOX:    
        case ZOOM:
        case ROTATE:
            undoAction.action = action;
        break;
    }
    setUndoStack(prev=>[...prev, {undoAction,redoAction}]);
    setRedoStack([]);
  };