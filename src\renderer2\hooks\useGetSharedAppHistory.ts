import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQuery<PERSON>eys } from "../common";

const useGetSharedAppHistory = () => {
  return useQuery({
    queryKey: [reactQueryKeys.getSharedAppHistory],
    queryFn: async () => {
      try {
        // const url = `${import.meta.env.VITE_API_SERVICE}/user/shared-app-history`;
        const url = `${import.meta.env.VITE_API_SERVICE}/user/app-shared`;
        const responseData = await axios.get(url);
        if (responseData.data && responseData.data.data) {
          if (
            typeof responseData.data.data === "object" &&
            "err_message" in responseData.data.data
          ) {
            throw new Error(responseData.data.data.err_message);
          } else {
            return responseData.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw error;
      }
    },
    staleTime: 0,
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};

export default useGetSharedAppHistory; 