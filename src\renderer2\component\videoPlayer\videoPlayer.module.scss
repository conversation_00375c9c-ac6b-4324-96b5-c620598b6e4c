.videoPlayerMain {
  position: relative;
  height: 300px;
  // background-color: rgba(0, 0, 0, 0.3);

  .videoLoading{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    .colorLoader{
      color: #16b9ff;
    }
  }

  video {
    width: 100%;
    height: 100%;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 90;
  }

  .VideoPlayIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 99;

    svg {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      box-shadow: inset -0.4px 0.4px 0.4px -0.9px rgba(255, 255, 255, 0.35);
      border-style: solid;
      border-width: 0.6px;
      border-image-source: linear-gradient(202deg, #fff 24%, rgba(255, 255, 255, 0) 15%);
      background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25)), linear-gradient(202deg, rgb(255 255 255 / 44%) 92%, rgba(255, 255, 255, 0) 15%);
      background-origin: border-box;
      background-clip: content-box, border-box;
      g{
        display: none;
      }
    }
  }
}

/* VideoPlayer.module.scss */

.custom-video-player {
  position: relative;
  height: 300px;
  /* Add any additional styles for the video player */
}

.fullScreenHeight {
  height: 100%;
}

.controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #000);
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  z-index: 99;
}

.time-display {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 2px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  text-align: left;
  color: #fff;
  width: 110px;

  .current-time,
  .duration {
    text-align: left;
  }

  span {
    white-space: nowrap;
  }
}

.seek-container {
  width: 100%;
}

.seek-bar {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, #16b9ff var(--progress, 0%), rgba(255, 255, 255, 0.2) 0%);
  outline: none;
  cursor: pointer;
  border-radius: 4px;
  position: relative;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 4px;
    height: 4px;
    cursor: pointer;
    background-color: transparent;
  }

}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 90;

  &.noOverlay {
    background-color: transparent;
  }
}

.VideoPlayIcon {
  width: 56px;
  height: 56px;
  fill: #ffffff;
  cursor: pointer;
}

.rightWindowPlayIcon{
  width: 88px;
  height: 110px;
  cursor: pointer;
  fill: #ffffff;

}

.volume-icon {
  color: #ffffff;
}

.volume-bar {
  width: 56px;
  height: 4px;
  appearance: none;
  background-color: rgba(156, 163, 175, 0.6);
  border-radius: 4px;
  outline: none;
  transition: background 0.3s;

  &::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #fff;
    cursor: pointer;
    margin-top: -4px; /* Center the thumb */
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #fff;
    cursor: pointer;
  }

  &::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 4px;
  }

  &::-moz-range-progress {
    background-color: #fff;
  }

  &::-moz-range-track {
    background-color: rgba(156, 163, 175, 0.6);
  }
}

.controls button {
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
  padding: 0;
  margin: 0;
  height: 24px;
}

.controls button:focus svg {
  outline: none;
}

.action-container {
  display: flex;
  flex-direction: row;
  align-items: center;

  .leftbar-action {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }
}

.playNextBtn{
  svg{
    width: 24px;
    height: 24px;
  }
  
  &:disabled {
    cursor: not-allowed;
    color: #fff !important;
    opacity: 0.5 !important;
  }
}

.loader {
  background-color: white;
}

.exitFullscreenIcon{
  svg{
    width: 28px;
    height: 28px;
  }
}