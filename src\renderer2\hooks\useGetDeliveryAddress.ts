// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";

const useGetDeliveryAddress = () => {

  return useQuery(
    [reactQueryKeys.getDeliveryAddress],
    async () => {
      try {
        const response = await axios.get(import.meta.env.VITE_API_ORDER_SERVICE + '/buyer/delivery-address');
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            throw new Error(response.data.data.error_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: navigator.onLine
    }
  );
};

export default useGetDeliveryAddress;
