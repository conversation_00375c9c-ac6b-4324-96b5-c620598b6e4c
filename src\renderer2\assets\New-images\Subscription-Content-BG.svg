<svg width="704" height="666" viewBox="0 0 704 666" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1171_2673)">
<rect width="704" height="666" rx="16" fill="#0F0F14"/>
<g opacity="0.8" filter="url(#filter0_f_1171_2673)">
<circle cx="644" cy="734" r="60" fill="#9786FF"/>
<circle cx="644" cy="734" r="60" fill="url(#paint0_linear_1171_2673)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_1171_2673_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.06 0.0785787 -0.056235 0.109785 644 738.873)"><foreignObject x="-945.056" y="-945.056" width="1890.11" height="1890.11"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="644" cy="734" r="60" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:119.99998474121094,&#34;m01&#34;:-112.46994018554688,&#34;m02&#34;:640.23498535156250,&#34;m10&#34;:157.15734863281250,&#34;m11&#34;:219.56929016113281,&#34;m12&#34;:550.5097656250},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g filter="url(#filter1_f_1171_2673)">
<circle cx="116" cy="-116" r="116" fill="#9786FF"/>
<circle cx="116" cy="-116" r="116" fill="url(#paint2_linear_1171_2673)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_1171_2673_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.116 0.151919 -0.108721 0.21225 116 -106.579)"><foreignObject x="-937.774" y="-937.774" width="1875.55" height="1875.55"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="116" cy="-116" r="116" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:231.99996948242188,&#34;m01&#34;:-217.44187927246094,&#34;m02&#34;:108.72095489501953,&#34;m10&#34;:303.83755493164062,&#34;m11&#34;:424.50064086914062,&#34;m12&#34;:-470.74777221679688},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="703" height="665" rx="15.5" stroke="url(#paint4_radial_1171_2673)" stroke-opacity="0.4"/>
<defs>
<filter id="filter0_f_1171_2673" x="524" y="614" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_1171_2673"/>
</filter>
<clipPath id="paint1_angular_1171_2673_clip_path"><circle cx="644" cy="734" r="60"/></clipPath><filter id="filter1_f_1171_2673" x="-100" y="-332" width="432" height="432" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1171_2673"/>
</filter>
<clipPath id="paint3_angular_1171_2673_clip_path"><circle cx="116" cy="-116" r="116"/></clipPath><linearGradient id="paint0_linear_1171_2673" x1="644" y1="710.244" x2="725.45" y2="754.714" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1171_2673" x1="116" y1="-161.929" x2="273.469" y2="-75.9536" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_1171_2673" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(154 -22.5) rotate(125.38) scale(126.943 120.692)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_1171_2673">
<rect width="704" height="666" rx="16" fill="white"/>
</clipPath>
</defs>
</svg>
