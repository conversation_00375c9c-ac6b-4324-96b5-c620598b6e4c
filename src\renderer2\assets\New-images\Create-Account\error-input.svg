<svg width="367" height="42" viewBox="0 0 367 42" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#2ne426ueja)">
        <rect x="1" y="1" width="365" height="40" rx="12" fill="url(#rja2f6vd9b)"/>
        <rect x=".5" y=".5" width="366" height="41" rx="12.5" stroke="url(#dhbuj41a8c)"/>
    </g>
    <defs>
        <linearGradient id="rja2f6vd9b" x1="-190.238" y1="-148.545" x2="35.608" y2="203.271" gradientUnits="userSpaceOnUse">
            <stop stop-color="#720D16"/>
            <stop offset="1" stop-color="#FF4859"/>
        </linearGradient>
        <linearGradient id="dhbuj41a8c" x1="224.212" y1="116.588" x2="221.436" y2="10.889" gradientUnits="userSpaceOnUse">
            <stop stop-color="#8C8B99"/>
            <stop offset="1" stop-color="#2F2E33"/>
        </linearGradient>
        <filter id="2ne426ueja" x="0" y="0" width="369.21" height="44.21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="2.21" dy="2.21"/>
            <feGaussianBlur stdDeviation="1.105"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_1338_4136"/>
        </filter>
    </defs>
</svg>
