{"env": {"browser": true, "es2021": true, "jest": true, "node": true}, "extends": ["eslint:recommended", "react-app", "plugin:react/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/electron", "plugin:import/typescript", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "ignorePatterns": ["**/*.js"], "plugins": ["react", "@typescript-eslint"], "rules": {"@typescript-eslint/naming-convention": ["warn", {"selector": "function", "format": ["PascalCase", "camelCase"]}], "react/prop-types": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "react/display-name": "off", "react/react-in-jsx-scope": "off", "quotes": ["error", "single"], "no-duplicate-imports": "error", "spaced-comment": ["error", "always", {"markers": ["/"]}], "@typescript-eslint/no-explicit-any": "off", "import/no-named-as-default": "off", "import/namespace": "off"}, "settings": {"import/resolver": {"typescript": {}}, "react": {"version": "detect"}}}