import React, { useEffect, useMemo, useState } from 'react'
import styles from './ListTab.module.scss';
import { useLeftPanelStore } from '../LeftPanelStore';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { MenuItem, Select } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { routes } from 'src/renderer2/common';
import SavedSearchList from '../Templates/SavedSearchList';
import clsx from 'clsx';

dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

// Define types for better TypeScript support
interface SearchProduct {
  id: string;
  title: string;
  created_date: string;
  time_stamp?: string;
  search_date_time: string;
  item_count: number;
  amount?: number;
  name?: string;
  products: Array<{ shape?: string }>;
  order_size: number;
}

interface GroupedData {
  [key: string]: SearchProduct[];
}

// Unified grouping function that works for all filters
const groupByTimeLabels = (items: SearchProduct[], sortOrder: 'newest' | 'oldest' = 'newest'): GroupedData => {
  const now = dayjs();
  const groups: GroupedData = {
    'Today': [],
    'Yesterday': [],
    'This Week': [],
    'Last Week': [],
    'This Month': [],
    'Last Month': [],
    'This Year': [],
    'Older': []
  };

  items?.forEach((item: SearchProduct) => {
    const itemDate = dayjs(item.created_date);
    
    if (itemDate.isToday()) {
      groups['Today'].push(item);
    } else if (itemDate.isYesterday()) {
      groups['Yesterday'].push(item);
    } else if (itemDate.isSame(now, 'week')) {
      groups['This Week'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'week'), 'week')) {
      groups['Last Week'].push(item);
    } else if (itemDate.isSame(now, 'month')) {
      groups['This Month'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'month'), 'month')) {
      groups['Last Month'].push(item);
    } else if (itemDate.isSame(now, 'year')) {
      groups['This Year'].push(item);
    } else {
      groups['Older'].push(item);
    }
  });

  // Remove empty groups
  Object.keys(groups).forEach(key => {
    if (groups[key].length === 0) {
      delete groups[key];
    }
  });

  // Sort groups based on sortOrder
  const groupOrder = {
    'newest': ['Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month', 'This Year', 'Older'],
    'oldest': ['Older', 'This Year', 'Last Month', 'This Month', 'Last Week', 'This Week', 'Yesterday', 'Today']
  };

  const sortedGroups: GroupedData = {};
  const order = groupOrder[sortOrder];
  
  order.forEach(groupName => {
    if (groups[groupName]) {
      sortedGroups[groupName] = groups[groupName];
    }
  });

  return sortedGroups;
};

// Filter functions for each menu item - all using the same grouping system
const filterByNewest = (data: SearchProduct[], field: string = 'created_date'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest');
  
  // Sort each group by the specified field (newest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      switch (field) {
        case 'created_date':
          return new Date(b.created_date).getTime() - new Date(a.created_date).getTime();
        case 'time_stamp':
          return new Date(b.time_stamp || b.created_date).getTime() - new Date(a.time_stamp || a.created_date).getTime();
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return new Date(b.created_date).getTime() - new Date(a.created_date).getTime();
      }
    });
  });
  
  return sortedGrouped;
};

const filterByOldest = (data: SearchProduct[], field: string = 'created_date'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'oldest');
  
  // Sort each group by the specified field (oldest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      switch (field) {
        case 'created_date':
          return new Date(a.created_date).getTime() - new Date(b.created_date).getTime();
        case 'time_stamp':
          return new Date(a.time_stamp || a.created_date).getTime() - new Date(b.time_stamp || b.created_date).getTime();
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return new Date(a.created_date).getTime() - new Date(b.created_date).getTime();
      }
    });
  });
  
  return sortedGrouped;
};

const filterByAToZ = (data: SearchProduct[], field: string = 'title'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest');
  
  // Sort each group alphabetically by the specified field (A to Z)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'created_date':
          return new Date(a.created_date).toISOString().localeCompare(new Date(b.created_date).toISOString());
        case 'time_stamp':
          return new Date(a.time_stamp || a.created_date).toISOString().localeCompare(new Date(b.time_stamp || b.created_date).toISOString());
        default:
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByZToA = (data: SearchProduct[], field: string = 'title'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest');
  
  // Sort each group alphabetically by the specified field (Z to A)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'title':
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
        case 'created_date':
          return new Date(b.created_date).toISOString().localeCompare(new Date(a.created_date).toISOString());
        case 'time_stamp':
          return new Date(b.time_stamp || b.created_date).toISOString().localeCompare(new Date(a.time_stamp || a.created_date).toISOString());
        default:
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByHighest = (data: SearchProduct[], field: string = 'amount'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest');
  
  // Sort each group by the specified field (highest to lowest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return (b.item_count || 0) - (a.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByLowest = (data: SearchProduct[], field: string = 'amount'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest');
  
  // Sort each group by the specified field (lowest to highest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return (a.item_count || 0) - (b.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const ListTab = () => {
  const location = useLocation();
  const savedSearchProducts = useLeftPanelStore(state => state.savedSearchProducts);
  const [grouped, setGrouped] = useState<GroupedData>({});
  const [viewFilter, setViewFilter] = useState<string>('newest');
  const [selectOpen, setSelectOpen] = useState<boolean>(false);
  
  // Single dropdown state
  const [dynamicDropdownOpen, setDynamicDropdownOpen] = useState<boolean>(false);
  const [dynamicDropdownValue, setDynamicDropdownValue] = useState<string>('date-created'); // Set default value

  // Dynamic options based on selected filter
  const getDynamicOptions = (filter: string=viewFilter) => {
    switch (filter) {
      case 'newest':
      case 'oldest':
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
          { value: 'date-modified', label: 'Date Modified', field: 'time_stamp' }
        ];
        
      case 'a-to-z':
      case 'z-to-a':
        return [
          { value: 'list-name', label: 'List Name', field: 'title' },
          { value: 'product-type', label: 'Product Type', field: 'product_type', disabled: true }
        ];
        
      case 'highest':
      case 'lowest':
        return [
          { value: 'item-count', label: 'Item Count', field: 'item_count' },
          { value: 'order-size', label: 'Order Size', field: 'order_size' }
        ];
        
      default:
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
          { value: 'date-modified', label: 'Date Modified', field: 'time_stamp' }
        ];
    }
  };

  const dynamicOptions = getDynamicOptions();

  const sortedData = useMemo(() => {
    // Use savedSearchProducts instead of undefined 'data'
    let data = savedSearchProducts || [];
    if(location.pathname === routes.homePage) data = savedSearchProducts
    else if(location.pathname === routes.savedBom) data = []
    else if(location.pathname === routes.createPoPage) data = []
    
    // Apply filtering based on both main filter and dynamic dropdown value
    let filteredData = data;
    
    // Map dynamic dropdown value to field name
    const getFieldFromDropdownValue = (dropdownValue: string): string => {
      switch (dropdownValue) {
        case 'date-created':
          return 'created_date';
        case 'date-modified':
          return 'time_stamp';
        case 'list-name':
          return 'title';
        case 'product-type':
          return 'product_type';
        case 'item-count':
          return 'item_count';
        case 'order-size':
          return 'order_size';
        default:
          return 'created_date';
      }
    };

    const fieldToSortBy = getFieldFromDropdownValue(dynamicDropdownValue);

    // Then apply the main filter sorting
    switch (viewFilter) {
      case 'newest':
        return filterByNewest(filteredData, fieldToSortBy);
        
      case 'oldest':
        return filterByOldest(filteredData, fieldToSortBy);
        
      case 'a-to-z':
        return filterByAToZ(filteredData, fieldToSortBy);
        
      case 'z-to-a':
        return filterByZToA(filteredData, fieldToSortBy);
        
      case 'highest':
        return filterByHighest(filteredData, fieldToSortBy);
        
      case 'lowest':
        return filterByLowest(filteredData, fieldToSortBy);
        
      default:
        return filterByNewest(filteredData, fieldToSortBy);
    }
  }, [viewFilter, dynamicDropdownValue, savedSearchProducts, location.pathname]);

  useEffect(() => {
    setGrouped(sortedData);
  }, [sortedData]);

  useEffect(()=>{
    if(location.pathname){
      setViewFilter('newest')
      // Set default value to first option when pathname changes
      const options = getDynamicOptions();
      setDynamicDropdownValue(options[0]?.value || '');
    }
  },[location.pathname])

  const handleViewFilterChange = (event: any) => {
    setViewFilter(event.target.value);
    // Set default value to first option when filter changes
    const newOptions = getDynamicOptions(event.target.value);
    setDynamicDropdownValue(newOptions[0]?.value || '');
  };

  const handleDynamicDropdownChange = (event: any) => {
    setDynamicDropdownValue(event.target.value);
  };

  return (
    <div className={styles.listTab}>
      <div className={styles.createNew}>
        <button>Create New</button>
      </div>
      <div className={styles.titleSection}>
        <span>
          {location.pathname === routes.savedBom ? <span className={clsx(styles.quoting, styles.title)}>QUOTING</span> : 
           location.pathname === routes.createPoPage ? <span className={clsx(styles.purchasing, styles.title)}>PURCHASING</span> : 
          //  location.pathname === routes.orderManagement ? <span className={clsx(styles.order, styles.title)}>ORDER MANAGEMENT</span> :
           <span className={clsx(styles.instantPriceSearch, styles.title)}>Instant Price Search</span>}
        </span>
      </div>
      <div className={styles.filterSection}>
        <div className={styles.filterSectionLeft}>
          <Select
            value={viewFilter}
            onChange={handleViewFilterChange}
            open={selectOpen}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            className={'selectDropdown'}
            MenuProps={
              {
                classes: {
                  paper: styles.dropDownBG
                },
              }
            }
          >
            <MenuItem value="newest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Newest</span>
              </div>
            </MenuItem>
            <MenuItem value="oldest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Oldest</span>
              </div>
            </MenuItem>
            <MenuItem value="a-to-z" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>A to Z</span>
              </div>
            </MenuItem>
            <MenuItem value="z-to-a" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Z to A</span>
              </div>
            </MenuItem>
            <MenuItem value="highest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Highest</span>
              </div>
            </MenuItem>
            <MenuItem value="lowest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Lowest</span>
              </div>
            </MenuItem>
          </Select>
        </div>
        {/* Single Dynamic Dropdown */}
        <div className={styles.filterSectionRight}>
          <Select
            value={dynamicDropdownValue}
            onChange={handleDynamicDropdownChange}
            open={dynamicDropdownOpen}
            onOpen={() => setDynamicDropdownOpen(true)}
            onClose={() => setDynamicDropdownOpen(false)}
            className={'selectDropdown'}
            MenuProps={{
              classes: {
                paper: styles.dropDownBG
              },
            }}
          >
            {dynamicOptions.map((option) => (
              <MenuItem 
                key={option.value} 
                value={option.value} 
                className={styles.menuItem}
                disabled={option.disabled}
              >
                <div className={styles.menuItemContent}>
                  <span>{option.label}</span>
                </div>
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
      <div className={styles.listSection}>
        <SavedSearchList groupedData={grouped} />
      </div>
    </div>
  )
}

export default ListTab
