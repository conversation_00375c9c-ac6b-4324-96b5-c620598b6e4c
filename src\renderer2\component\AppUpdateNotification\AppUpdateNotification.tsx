import React, { forwardRef } from 'react';
import styles from './AppUpdateNotification.module.scss';
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../assets/New-images/Logo.svg';

interface AppUpdateNotificationProps {
  updateUrl?: string;
  channelWindow: any;
}

const AppUpdateNotification = forwardRef<HTMLDivElement, AppUpdateNotificationProps>(({
  updateUrl,
  channelWindow
}, ref) => {
  const handleUpdateClick = () => {
    if (updateUrl) {
      window.open(updateUrl, '_blank');
    }
  };
  const onClose = () => {
    if (channelWindow?.close) {
      window.electron.send({ channel: channelWindow.close })
    }
  }
  const onMinimize = () => {
    if (channelWindow?.minimize) {
      window.electron.send({ channel: channelWindow.minimize })
    }
  }

  return (
    <div ref={ref} className={styles.container}>
      <div className={styles.content}>
      <div className={styles.appDrag}></div>
        {/* Window Controls */}
        <div className={styles.windowControls}>
          {onMinimize && (
            <button
              className={styles.minimizeBtn}
              onClick={onMinimize}
              type="button"
              title="Minimize"
            >
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M2 6H10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
              </svg>
            </button>
          )}
          {onClose && (
            <button
              className={styles.closeBtn}
              onClick={onClose}
              type="button"
              title="Close"
            >
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M9 3L3 9M3 3L9 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
              </svg>
            </button>
          )}
        </div>

        <div className={styles.appUpdateNotifiMain}>
          <div className={styles.icon}>
            <BryzosLogo />
          </div>
          <h2 className={styles.title}>New App Version Available</h2>
          <p className={styles.message}>
            New app version is available please wait for update or you can{' '}
          </p>
          <button
            className={styles.updateLink}
            onClick={handleUpdateClick}
            type="button"
          >
            click here to download
          </button>

        </div>

      </div>
    </div>
  );
});

AppUpdateNotification.displayName = 'AppUpdateNotification';

export default AppUpdateNotification; 