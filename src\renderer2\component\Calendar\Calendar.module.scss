.calendarWrapper {
  position: relative;
}

.deliverByButton1 {
  width: 100%;
  height: 40px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  display: flex;
  border: 1px solid transparent;
  font-family: Inter;
  font-size: 14px;
  letter-spacing: 0.56px;
  color: #fff;

  &:hover {
    border-color: #459fff;
  }
  .leftSideSpan {
    width: 72px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.04);
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;
  }
  .rightSideSpan {
    color: #fff;
    width: calc(100% - 72px);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
  }
}
.deliverByButton2 {
  width: 100%;
  height: 40px;
  padding: 12px 20px;    
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.56px;
  text-align: left;
  color: #616575;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    border-color: #459fff;
  }
  &:focus-visible {
    outline: none;
    border-color: #459fff;
  }
}

.dateButton {
  background: #2a2a2a;
  border: none;
  border-radius: 5px;
  color: white;
  cursor: pointer;
  padding: 10px 15px;
  font-size: 1rem;
  width: 100%;
  text-align: left;
  
  &:hover {
    background: #3a3a3a;
  }
}

.calendarContainer {
  box-shadow: inset 4px 4px 10.1px 0 #000;
  background-origin: border-box;
  background-clip: content-box, border-box;
  width: 250px;
  height: 188px;
  border-radius: 13px;
  border: solid 1px transparent;
  background: url(../../assets/New-images/CreatePODatePickerBG.svg) no-repeat transparent;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  padding: 12px 10px;
  overflow: hidden;
}

.calendarHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  padding-bottom: 12px;

  .monthYear {
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 3.06px;
    text-align: left;
    color: #dfe2f0;
    text-transform: uppercase;
  }
}

.weekdaysContainer {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 15px;
  padding-bottom: 4px;
  text-align: center;
  .weekday {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: bold;
    line-height: normal;
    letter-spacing: 0.4px;
    text-align: center;
    color: #9b9eac;
  }
}

.daysGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  column-gap: 15px;
  row-gap: 4px;
}

.dayCell {
  height: 20px;
  font-family: Inter;
  font-size: 15px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0.6px;
  text-align: center;
  color: #fff;
  width: 20px;
  margin: 0 0;
  transition: all 0.2s ease;
  position: relative;

  &:hover:not(.disabled) {
    font-weight: bold;
  }

  &.dayCell.today {
    color: #459fff;
  }

  &.otherMonth {
    color: #666;
  }

  &.disabled {
    color: #444;
    opacity: 0.7;
    cursor: default;
  }

  &.focused {
    position: relative;
    color: white;
    outline: none;
    font-weight: bold;
    
    // &::before {
    //   content: '';
    //   position: absolute;
    //   top: 50%;
    //   left: 50%;
    //   transform: translate(-50%, -50%);
    //   width: 26px;
    //   height: 26px;
    //   background: transparent;
    //   border: 2px solid #3a3a3a;
    //   border-radius: 50%;
    //   z-index: -1;
    // }
  }

  &.selected {
    position: relative;
    color: white;
    font-weight: bold;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 28px;
      height: 28px;
      background: #459fff;
      border-radius: 50%;
      z-index: -1;
    }
    &.focused {
      outline-color: #fff;
    }
  }

  &:focus {
    outline: none;
  }
} 