
import clsx from 'clsx';
import {
    useState,
    useEffect,
    useRef
} from 'react';
import styles from './Impersonate.module.scss'
import { useHeightListener } from '../../hooks/useHeightListener';
import { useGetAllUsers, useGlobalStore, filterArrray  , useAuthStore} from '@bryzos/giss-ui-library';
import axios from 'axios';
import DialogPopup from '../DialogPopup/DialogPopup';
import { commomKeys, routes, userRole } from 'src/renderer2/common';
import { getHomeRoute, navigatePage } from 'src/renderer2/helper';
import SearchHeader from 'src/renderer2/pages/SearchHeader';

function Impersonate({appVersion}) {
    const ref = useHeightListener(appVersion);
    const { setShowLoader, userData ,setHideHeaderState } = useGlobalStore();
    const { data: allUsers, isLoading: isGetAllUsersLoading, isError:isGetAllUsersError, error:getAllUsersError } = useGetAllUsers()
    const [userList, setUserList] = useState([]);
    const [filterUserList, setFilterUserList] = useState([]);
    const [errorMessage, setErrorMessage] = useState('');
    const [searchString, setSearchString] = useState('');
    const  {impersonateLogin} = useAuthStore()

    useEffect(() => {
        if (allUsers && !isGetAllUsersLoading) {
            setUserList(allUsers);
            setFilterUserList(allUsers);
            setShowLoader(false);
        }
        if(isGetAllUsersError){
            if(getAllUsersError.message){
                setErrorMessage(getAllUsersError.message);
            }
        }
    }, [allUsers, isGetAllUsersLoading, isGetAllUsersError])

    const handleSelectedUserLogin = async(impersonateUserData) => {
        try{
            setHideHeaderState(true)
            setShowLoader(true);
            const response = await axios.get(import.meta.env.VITE_API_SERVICE +'/admin/getUserAccessToken?userKey='+impersonateUserData.unique_key,{headers:{
                'email-id': userData?.data?.email_id
            }});
            const token = response.data.data;
            if(typeof response.data.data === 'string'){
                await impersonateLogin(token,impersonateUserData)
            }else{
                setErrorMessage(response.data.data.error_message);
                setShowLoader(false);
                setHideHeaderState(false)
            }
        }catch(error) {
            console.log(error);
            if(error?.response){
                let errorMessage = error?.message;
                if(error.response?.status === 400){
                    errorMessage = error.response?.data?.message;
                }
                setErrorMessage(errorMessage);
            }
            setShowLoader(false);
            setHideHeaderState(false)
        }
    }
    const search = (e) => {
        setSearchString(e.target.value);
        if (e.target.value) {
            const _filterArrray = filterArrray(userList, e.target.value, [
                "email_id",
                "first_name",
                "last_name",
                "company_name",
            ]);
            if (_filterArrray?.length) {
                setFilterUserList(_filterArrray);
            } else {
                setFilterUserList([]);
            }
        } else {
            setFilterUserList(userList ? userList : []);
        }
    }

    const handleErrorPopupClose = ()=>{
        setErrorMessage('');
        setShowLoader(true);
        navigatePage(location.pathname, {path: getHomeRoute()})
    }

    return (
        <>
          <div>
                <SearchHeader />
              </div>
            <div className={clsx(styles.impersonateInnerContent, 'bgBlurContent')} ref={ref}>
                <div className={styles.impersonateSearchHeader}>
                     <input type="text" placeholder='Search User' value={searchString} onChange={search} />
                 </div>
                 <div className={styles.userListTable}>
                 <div className={clsx(styles.userListTableContent)}>
                    <div className={styles.tableGrid}>
                        <table>
                            <thead>
                                <tr>
                                    <th><span>First Name</span></th>
                                    <th><span>Last Name</span></th>
                                    <th><span>Email</span></th>
                                </tr>
                            </thead>
                            <tbody>
                            {filterUserList.map((user) => (
                                <tr key={user.id} onClick={()=>{handleSelectedUserLogin(user)}}>
                                    <td>{user.first_name}</td>
                                    <td>{user.last_name}</td>
                                    <td>{user.email_id}</td>
                                </tr>
                             ))}
                            </tbody>
                        </table>
                       
                     
                    </div>
                </div>
                 </div>
          
            </div>
            <DialogPopup
                dialogContent={errorMessage}
                dialogBtnTitle={commomKeys.errorBtnTitle}
                type={commomKeys.actionStatus.error}
                open={!!errorMessage}
                onClose={() => handleErrorPopupClose()}
            />
        </>
    );
}
export default Impersonate;