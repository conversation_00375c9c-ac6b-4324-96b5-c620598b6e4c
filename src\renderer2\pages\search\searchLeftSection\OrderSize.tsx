import React, { useEffect, useState } from 'react'

import styles from '../home.module.scss'
import OrderSizeFilter from '../../../component/OrderSizeFilter/OrderSizeFilter'
import usePostSaveDefaultOrderSize from 'src/renderer2/hooks/usePostSaveDefaultOrderSize'
import { useSearchStore } from 'src/renderer2/store/SearchStore';
const OrderSize = () => {
    const { mutate: saveDefaultOrderSize } = usePostSaveDefaultOrderSize();
    const { orderSizeSliderValue } = useSearchStore();
    const [orderSizeSaveDefault ,setOrderSizeSaveDefault]= useState(false);

    const handleSaveDefaultOrderSize = () => {
        setOrderSizeSaveDefault(false);
        saveDefaultOrderSize({
            order_size: String(orderSizeSliderValue)
        });
    }
    return (
        <div className={styles.orderFilter} data-hover-video-id='order-size-on-search'>
            <div className={styles.leftFilterTitle}>ORDER SIZE (LB)</div>
            <OrderSizeFilter setOrderSizeSaveDefault={setOrderSizeSaveDefault} />
            <button disabled={!orderSizeSaveDefault} className={styles.saveDefaultBtn} onClick={handleSaveDefaultOrderSize}>SAVE AS DEFAULT</button>
        </div>
    )
}

export default OrderSize