<svg width="464" height="474" viewBox="0 0 464 474" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1422_325)">
<rect x="12" y="16" width="436" height="442" rx="14" fill="url(#paint0_linear_1422_325)"/>
</g>
<g filter="url(#filter1_d_1422_325)">
<path d="M12 22C12 14.268 18.268 8 26 8H434C441.732 8 448 14.268 448 22V99H12V22Z" fill="url(#paint1_linear_1422_325)"/>
<path d="M26 8.5H434C441.456 8.5 447.5 14.5442 447.5 22V98.5H12.5V22C12.5 14.5442 18.5442 8.5 26 8.5Z" stroke="url(#paint2_linear_1422_325)"/>
</g>
<g filter="url(#filter2_d_1422_325)">
<path d="M12 369H448V444C448 451.732 441.732 458 434 458H26C18.268 458 12 451.732 12 444V369Z" fill="url(#paint3_linear_1422_325)"/>
<path d="M447.5 369.5V444C447.5 451.456 441.456 457.5 434 457.5H26C18.5442 457.5 12.5 451.456 12.5 444V369.5H447.5Z" stroke="url(#paint4_linear_1422_325)"/>
</g>
<defs>
<filter id="filter0_d_1422_325" x="4.9" y="8.9" width="458.2" height="464.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1422_325"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1422_325"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1422_325" result="shape"/>
</filter>
<filter id="filter1_d_1422_325" x="4.9" y="0.9" width="458.2" height="113.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1422_325"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1422_325"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1422_325" result="shape"/>
</filter>
<filter id="filter2_d_1422_325" x="0.9" y="355.9" width="458.2" height="111.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1422_325"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.34 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1422_325"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1422_325" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1422_325" x1="524" y1="851.215" x2="-220.351" y2="202.662" gradientUnits="userSpaceOnUse">
<stop stop-color="#42454F"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint1_linear_1422_325" x1="524" y1="179.956" x2="454.755" y2="-113.092" gradientUnits="userSpaceOnUse">
<stop stop-color="#42454F"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint2_linear_1422_325" x1="134" y1="-96.5" x2="136.5" y2="38.1629" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#14141A"/>
</linearGradient>
<linearGradient id="paint3_linear_1422_325" x1="524" y1="537.177" x2="457.612" y2="249.909" gradientUnits="userSpaceOnUse">
<stop stop-color="#42454F"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint4_linear_1422_325" x1="141.5" y1="176" x2="136.5" y2="398.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#14141A"/>
</linearGradient>
</defs>
</svg>
