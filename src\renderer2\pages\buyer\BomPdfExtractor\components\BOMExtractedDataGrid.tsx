import React, { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import type { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'; // Import all modules
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";

const INITIAL_COLUMNS: ColDef[] = [];

const BOMExtractedDataGrid: React.FC = () => {
  const gridRef = useRef<AgGridReact>(null);
  const [gridColumnDefs, setGridColumnDefs] = useState<ColDef[]>(INITIAL_COLUMNS);
  const [rowData, setRowData] = useState<any[]>([]);
  const {columnDef, setColumnDef} = useBomPdfExtractorStore();
  
  useEffect(()=>{
    setGridColumnDefs([...columnDef]);
  },[columnDef]);


  return (
    <div>
      <div className="ag-theme-alpine" style={{ height: "100%", width: "100%" }}>
        <AgGridReact
          ref={gridRef}
          columnDefs={gridColumnDefs}
          rowData={rowData}
          domLayout="autoHeight"
        />
      </div>
    </div>
  );
};

export default BOMExtractedDataGrid;
