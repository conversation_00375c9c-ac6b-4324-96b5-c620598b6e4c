/**
 * Utility functions for mapping user-drawn boxes to AWS Textract data
 */

/**
 * Check if two bounding boxes intersect
 * @param {Object} box1 - First bounding box {Left, Top, Width, Height}
 * @param {Object} box2 - Second bounding box {Left, Top, Width, Height}
 * @returns {boolean} - True if boxes intersect
 */
export function doBoxesIntersect(box1, box2) {
  // Check if one box is to the left of the other
  if (box1.Left + box1.Width < box2.Left || box2.Left + box2.Width < box1.Left) return false;
  
  // Check if one box is above the other
  if (box1.Top + box1.Height < box2.Top || box2.Top + box2.Height < box1.Top) return false;
  
  // Boxes must intersect
  return true;
}

/**
 * Calculate the area of intersection between two boxes
 * @param {Object} box1 - First bounding box {Left, Top, Width, Height}
 * @param {Object} box2 - Second bounding box {Left, Top, Width, Height}
 * @returns {number} - Area of intersection
 */
export function getIntersectionArea(box1, box2) {
  // Calculate intersection dimensions
  const xOverlap = Math.max(
    0,
    Math.min(box1.Left + box1.Width, box2.Left + box2.Width) - Math.max(box1.Left, box2.Left)
  );
  
  const yOverlap = Math.max(
    0,
    Math.min(box1.Top + box1.Height, box2.Top + box2.Height) - Math.max(box1.Top, box2.Top)
  );
  
  // Return area of intersection
  return xOverlap * yOverlap;
}

/**
 * Calculate the overlap ratio between two boxes
 * @param {Object} box1 - First bounding box {Left, Top, Width, Height}
 * @param {Object} box2 - Second bounding box {Left, Top, Width, Height}
 * @returns {number} - Ratio of intersection area to the smaller box's area
 */
export function getOverlapRatio(box1, box2) {
  const intersectionArea = getIntersectionArea(box1, box2);
  const box1Area = box1.Width * box1.Height;
  const box2Area = box2.Width * box2.Height;
  const smallerArea = Math.min(box1Area, box2Area);
  
  return smallerArea > 0 ? intersectionArea / smallerArea : 0;
}

/**
 * Convert canvas coordinates to normalized Textract coordinates (0-1 range)
 * @param {Object} rect - Rectangle in canvas coordinates {x, y, width, height}
 * @param {Object} pdfDimensions - PDF dimensions {width, height}
 * @returns {Object} - Normalized coordinates {Left, Top, Width, Height}
 */
export function normalizeBoxCoordinates(rect, pdfDimensions) {
  return {
    Left: rect.x / pdfDimensions.width,
    Top: rect.y / pdfDimensions.height,
    Width: rect.width / pdfDimensions.width,
    Height: rect.height / pdfDimensions.height
  };
}

/**
 * Map user-drawn boxes to Textract blocks
 * @param {Array} userBoxes - Array of user-drawn boxes
 * @param {Object} textractData - AWS Textract response data
 * @param {Object} pdfDimensions - PDF dimensions {width, height}
 * @param {number} pageNumber - Current page number
 * @returns {Object} - Mapping of box IDs to extracted text
 */
export function mapBoxesToTextractData(userBoxes, textractData, pdfDimensions, pageNumber) {
  const results = {};
  
  // Filter Textract blocks for the current page
  const pageBlocks = textractData.Blocks.filter(block => 
    (block.Page === pageNumber || !block.Page) && // Some blocks might not have page info
    (block.BlockType === 'LINE' || block.BlockType === 'WORD')
  );
  
  userBoxes.forEach(box => {
    // Skip boxes without rect data
    if (!box.rect) return;
    
    // Normalize box coordinates to 0-1 range (Textract uses this range)
    const normalizedBox = normalizeBoxCoordinates(box.rect, pdfDimensions);
    
    // Find intersecting Textract blocks with significant overlap
    const intersectingBlocks = pageBlocks.filter(block => {
      if (!block.Geometry?.BoundingBox) return false;
      
      const overlap = getOverlapRatio(normalizedBox, block.Geometry.BoundingBox);
      return overlap > 0.1; // At least 10% overlap to consider it relevant
    });
    
    // Sort blocks by vertical position (top to bottom)
    const sortedBlocks = intersectingBlocks.sort((a, b) => 
      a.Geometry.BoundingBox.Top - b.Geometry.BoundingBox.Top
    );
    
    // Extract text from intersecting blocks
    const extractedText = sortedBlocks.map(block => block.Text).join(' ');
    
    // Store result by box ID
    results[box.id] = {
      type: box.type,
      text: extractedText,
      blocks: sortedBlocks.map(block => block.Id)
    };
  });
  
  return results;
}

/**
 * Process Textract data to make it easier to work with
 * @param {Object} textractData - Raw AWS Textract response
 * @returns {Object} - Processed data with additional indices
 */
export function processTextractData(textractData) {
  // Create a map of block IDs to blocks for quick lookup
  const blockMap = {};
  textractData.Blocks.forEach(block => {
    blockMap[block.Id] = block;
  });
  
  // Add the block map to the data
  return {
    ...textractData,
    blockMap,
    // Add any other useful indices or processed data here
  };
}
