import * as yup from 'yup';

export const userSchema = yup.object().shape({
  // Placeholder for User tab fields
  userType: yup.string(),
  firstName: yup.string(),
  email: yup.string().email('Invalid email format'),
  phoneNumber: yup.string(),
  searchZipcode: yup.string(),
  stateSubscription: yup.array().of(yup.number()).default([]),
});

export type UserFormData = yup.InferType<typeof userSchema>;
