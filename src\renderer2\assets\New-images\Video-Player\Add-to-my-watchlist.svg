<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-8.16547" y="-8.16547" width="38.33" height="38.3309"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.33px);clip-path:url(#bgblur_0_2323_1210_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_2323_1210)" data-figma-bg-blur-radius="8.66547">
<ellipse cx="10.9997" cy="11" rx="9.99973" ry="10" fill="white" fill-opacity="0.1"/>
<path d="M11 0.75C16.6607 0.750145 21.249 5.33918 21.249 11C21.249 16.6608 16.6607 21.2499 11 21.25C5.33922 21.25 0.75 16.6609 0.75 11C0.75 5.33909 5.33922 0.75 11 0.75Z" stroke="url(#paint0_linear_2323_1210)" stroke-opacity="0.2" stroke-width="0.5"/>
</g>
<path d="M11.001 7V15M15.001 11H7.00098" stroke="white" stroke-width="1.2"/>
<defs>
<filter id="filter0_i_2323_1210" x="-8.16547" y="-8.16547" width="38.33" height="38.3309" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.632776" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_2323_1210"/>
<feOffset dx="-0.316388" dy="0.316388"/>
<feGaussianBlur stdDeviation="0.158194"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2323_1210"/>
</filter>
<clipPath id="bgblur_0_2323_1210_clip_path" transform="translate(8.16547 8.16547)"><ellipse cx="10.9997" cy="11" rx="9.99973" ry="10"/>
</clipPath><linearGradient id="paint0_linear_2323_1210" x1="18.6608" y1="2.37097" x2="10.9994" y2="20.9999" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
