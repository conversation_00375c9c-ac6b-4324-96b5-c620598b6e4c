.videoPlayerRightWindowContainer {
  width: 322px;
  height: 450px;
  flex-grow: 0;
  border-radius: 20px;
  border-style: solid;
  border-width: 1px;
  border-color: transparent;
  background-image: linear-gradient(340deg, #2b2d33 200%, #0f0f14 -67%);        
  background-origin: border-box;
  background-clip: content-box, border-box;
  overflow: hidden;
  position: relative;
}

.videoPlayerSection {
  width: 100%;
  height: 275px;
  overflow: hidden;
}

.videoPlayer {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.informationContainer{
  width: 322px;
  height: 175px;
  padding: 24px 22px 50px 20px;
  box-shadow: 0 -16px 15.1px -11px #000;
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(180deg, #fff -112%, #1a1b21 28%);
  border-image-slice: 1;
  background-image: linear-gradient(162deg, #0f0f14 -26%, #393e47 226%);
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  .title{
      font-family: Syncopate;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: 1.12px;
      text-align: left;
      color: rgba(255, 255, 255, 0.8);
      text-transform: uppercase;
  }
  .description{
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.15;
      letter-spacing: normal;
      text-align: left;
      color: rgba(255, 255, 255, 0.8);
  }
}

.closeButton {
  position: absolute;
  top: 9px;
  right: 13px;
  width: auto;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  z-index: 100;
  margin: 0;
  padding: 0;
} 