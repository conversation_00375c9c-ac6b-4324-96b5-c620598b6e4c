import { readdirSync, statSync } from 'fs';
import { join } from 'path';

const ignoreFilesExtension = {
  'win32': ['zip']
}

const extensionToignore = ignoreFilesExtension[process.platform]

function getAllFilesRecursively(directoryPath) {
    const files = [];
  
    function traverseDirectory(currentPath) {
      const items = readdirSync(currentPath);
  
      items.forEach(item => {
        const itemPath = join(currentPath, item);
        const itemStats = statSync(itemPath);
        
        if (itemStats.isDirectory()) {
          traverseDirectory(itemPath); // Recursively traverse subdirectories
        } else if (itemStats.isFile() && !extensionToignore?.some(x=> itemPath.includes(x))) {
         files.push(itemPath);
        }
      });
    }
  
    traverseDirectory(directoryPath);
    return files;
  }

const directoryPath = '../../out/make'; // Change this to your desired directory path
export const filesInDirectory = getAllFilesRecursively(directoryPath);


function padTo2Digits(num) {
  return num.toString().padStart(2, '0');
}

export function formatDate(date = new Date()) {
  return [
    padTo2Digits(date.getDate()),
    padTo2Digits(date.getMonth() + 1),
    date.getFullYear(),
  ].join('-');
}
