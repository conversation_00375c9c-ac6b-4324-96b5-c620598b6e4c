import type { Configuration } from 'webpack';
import { rules } from './webpack.rules';
import path from 'path';
export const preloadConfig: Configuration = {
  module: {
    rules,
  },
  plugins: [],
  resolve: {
    modules: [path.resolve(__dirname), 'node_modules'],
    extensions: ['.js', '.ts', '.jsx', '.tsx', 'css'],
  },
  externals: {
    // AWS SDK and related modules
    'aws-sdk': 'commonjs aws-sdk',
    'nock': 'commonjs nock',
    'mock-aws-s3': 'commonjs mock-aws-s3',
    'aws-sdk-mock': 'commonjs aws-sdk-mock',
    // Node.js built-in modules
    'fs': 'commonjs fs',
    'path': 'commonjs path',
    'os': 'commonjs os',
    'crypto': 'commonjs crypto',
    'util': 'commonjs util',
    'events': 'commonjs events',
    'stream': 'commonjs stream',
    'buffer': 'commonjs buffer',
    'url': 'commonjs url',
    'querystring': 'commonjs querystring',
    'http': 'commonjs http',
    'https': 'commonjs https',
    'net': 'commonjs net',
    'tls': 'commonjs tls',
    'zlib': 'commonjs zlib',
    'child_process': 'commonjs child_process',
  },
};
