import { priceUnits, useGlobalStore } from "@bryzos/giss-ui-library";
import { fetchPrice } from "src/renderer2/helper";
import { useSearchStore } from "src/renderer2/store/SearchStore";
import { useLeftPanelStore } from "../LeftPanelStore";
import styles from "../ListTab/ListTab.module.scss";
import { ReactComponent as DeleteIcon } from "../../../assets/New-images/New-Image-latest/delete-outlined.svg";
import { ReactComponent as ShareIcon } from "../../../assets/New-images/New-Image-latest/share-outlined.svg";
import { ReactComponent as EditIcon } from "../../../assets/New-images/New-Image-latest/pencil-outlined.svg";

const SavedSearchList = ({ groupedData }: { groupedData: any }) => {
    
    const handleItemClick = async (item: any) => {
        const selectedSavedSearch = useLeftPanelStore.getState().selectedSavedSearch;
        const setSelectedSavedSearch = useLeftPanelStore.getState().setSelectedSavedSearch;
        if(item.id === selectedSavedSearch?.id)return;
        setSelectedSavedSearch(item);
        const referenceData = useGlobalStore.getState().referenceData;
        const setOrderSizeSliderValue = useSearchStore.getState().setOrderSizeSliderValue;
        const setSelectedPriceUnit = useSearchStore.getState().setSelectedPriceUnit;
        const setSearchZipCode = useSearchStore.getState().setSearchZipCode;
        const brackets = referenceData?.ref_weight_price_brackets || [];
        let orderSize = Number(item.order_size);
        
        // Find the appropriate bracket based on order size
        const matchingBracket = brackets.find((bracket: any, index: number) => {
            // For last bracket, only check min since it's unlimited max
            if (index === brackets.length - 1) {
                return orderSize >= Number(bracket.min_weight);
            }
            // For other brackets check if order size falls between min and max
            return orderSize >= Number(bracket.min_weight) && orderSize < Number(brackets[index + 1].min_weight);
        });

        if (matchingBracket) {
            orderSize = matchingBracket.min_weight;
        }
        setOrderSizeSliderValue(Number(orderSize));
        setSearchZipCode(item.zipcode);
        setSelectedPriceUnit(item.products[0].price_unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : item.products[0].price_unit.toLowerCase());
        const setShortListedSearchProductsData = useSearchStore.getState().setShortListedSearchProductsData;
        const shortListedSearchProductsData = useSearchStore.getState().shortListedSearchProductsData;
        if (shortListedSearchProductsData.length > 0) {
            setShortListedSearchProductsData([]);
        }

        fetchPrice(item.products, item.zipcode, parseFloat(item.order_size.replace(/[$,]/g, "")));
       
    };

    return (
        <div className={styles.savedSearchListContainer}>
            {
                Object.keys(groupedData).length > 0 ?
                    Object.entries(groupedData).map(([label, items]: any, index: number) => (
                        <div key={index} className={styles.searchContainer}>
                            <p className={styles.searchLabel}>{label}</p>
                            {
                                items.map((item: any) => (
                                    <div className={styles.searchItemContainer} key={item.id} onClick={() => handleItemClick(item)}>
                                        <div className={styles.searchTitle}>
                                            <span className={styles.searchTitleText}>{item.title} <EditIcon /></span>
                                            <span className={styles.itemCount}>{item.item_count} Items</span>
                                            <div className={styles.iconContainer}>
                                                <ShareIcon />
                                                <DeleteIcon />
                                            </div>
                                        </div>
                                        <div className={styles.searchDetails}>
                                            {
                                                Array.from(new Set(
                                                    item?.products
                                                        .map((obj: any) => obj?.shape ?? '')
                                                )).join(', ')
                                            }
                                            <span>beam</span><br/>
                                            <span>Based Upon {item.order_size} LBS</span><br />
                                            <span>{item.search_date_time}</span>
                                        </div>
                                        
                                    </div>
                                ))
                            }
                        </div>
                    ))
                    :
                    <div>
                        <p>Today</p>
                        <span>Your Instant Pricing activity will be saved here</span>
                    </div>
            }
        </div>
    );
}
export default SavedSearchList;