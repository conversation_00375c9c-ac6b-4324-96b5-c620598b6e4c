// @ts-nocheck
import { memo, useEffect, useRef, useState } from 'react';
import styles from './acceptOrder.module.scss';
import { ReactComponent as ScrollDownImage } from '../../assets/images/scroll-down-image.svg';
import { ReactComponent as WarningIcon } from '../../assets/images/warning-seller-icon.svg';
import { routes, purchaseOrder, referenceDataKeys, userRole } from '../../common';
import { useLocation, useNavigate } from 'react-router';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../assets/images/tnc-close.svg';
import { ReactComponent as CheckIcon } from '../../assets/New-images/component-2-checked.svg';
import { ReactComponent as UncheckIcon } from '../../assets/New-images/component-2-uncheck.svg';
import clsx from 'clsx';
import moment from 'moment';
import { useGlobalStore, useSellerOrderStore, getSocketConnection, getChannelWindow, priceUnits } from '@bryzos/giss-ui-library';
import 'moment-timezone';
import { format4DigitAmount, formatToTwoDecimalPlaces } from '../../helper';
import ReminderYouAreAlmostTherePopup from './ReminderYouAreAlmostTherePopup/ReminderYouAreAlmostTherePopup';
import useSaveSellerViewedOrder from '../../hooks/useSaveSellerViewedOrder';
import { CommonTooltip } from '../../component/Tooltip/tooltip';
import OrderConfirmationSeller from './orderConfirmation';
import PdfMakePage from '../PdfMake/pdfMake';
import { descriptionLines, getOtherDescriptionLines } from '../../utility/pdfUtils';
import SearchHeader from '../SearchHeader';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import ClaimOrderRightWindow from 'src/renderer2/component/ClaimOrderRightWindow/ClaimOrderRightWindow';
const SubmitApplicationDialog = (props) => {

    const [hasAcceptedTnC, setHasAcceptedTnC] = useState(false);

    const tncChangeHandler = ($event) => {
        $event.stopPropagation();
        setHasAcceptedTnC(state => !state);
    }

    return (
        <Dialog
            open={props.open ?? false}
            onClose={props.onClose}
            transitionDuration={200}
            hideBackdrop
            container={props.dialogRef}
            style={{
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(7px)',
                WebkitBackdropFilter: 'blur(7px)',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '0px 0px 16px 16px'
            }}
            classes={{
                root: styles.SubmitApp,
                paper: styles.dialogContent,
            }}
        >
            <div className={styles.disclaimer}>
                IMPORTANT
            </div>
            <ul>
                <li>All delivered material must be new, prime, within the stated tolerance per the product specification and include mill test reports.</li>
                <li>Material must be packaged & loaded for forklift or magnetic offloading.</li>
                <li>Strip load only, no pyramid loading for pipe.</li>
                <li>Material must be reasonably free of oxidation and pitting.</li>
                <li>Maximum bundle weight is 5,000 pounds.</li>
                <li>By “Accepting Order,” you agree to fulfill this order (including delivery, fuel surcharges and other fulfillment-related items) at the published price.  The only<br /> acceptable variance between the published price and your future invoices will be reasonable quantity reconciliations (ie. ordered vs shipped).</li>
            </ul>

            <label className={styles.agreeCheckbox}>
                <input type='checkbox' checked={hasAcceptedTnC} onChange={($event) => { tncChangeHandler($event) }} />
                <span className={styles.checkmark}>
                    {hasAcceptedTnC ? (
                        <CheckIcon className={styles.checkIcon} />
                    ) : (
                        <UncheckIcon className={styles.uncheckIcon} />
                    )}
                </span>
                <span className={styles.lblChk}>
                    I understand and agree.
                </span>
            </label>
            <div className={styles.flx}>
                <button onClick={props.onClose} className={styles.cancelBtn}>CANCEL</button>
                <button className={styles.submitBtn} onClick={($event) => { props.acceptOrder($event); }} disabled={!hasAcceptedTnC}><span>ACCEPT ORDER</span></button>
            </div>
        </Dialog>
    );
};

const AcceptOrder = () => {
    const dialogRef = useRef(null);
    const navigate = useNavigate();
    const location = useLocation();
    const { setLoadComponent, setProps} = useRightWindowStore();
    const channelWindow = getChannelWindow();
    const { showPopup, isEditMode, index } = location.state;
    const [showScrollDownToSee, setShowScrollDownToSee] = useState(true);
    const { userData, originalLoggedInUserData, referenceData } = useGlobalStore();
    const stateRef = referenceData?.ref_states;
    const [poIndex, setPoIndex] = useState(index);
    const filteredPoList = useSellerOrderStore(state => state.filteredPoList);
    const setPOCart = useSellerOrderStore(state => state.setPOCart);
    const ordersCart = useSellerOrderStore(state => state.ordersCart);
    const changePoToBeAccepted = useSellerOrderStore(state => state.changePoToBeAccepted);
    const navigatePageTo = useSellerOrderStore(state => state.navigatePageTo);
    const setNavigatePageTo = useSellerOrderStore(state => state.setNavigatePageTo);
    const orderDetail = useSellerOrderStore(state => state.orderToBeShownInOrderAccept);
    const setOrderDetail = useSellerOrderStore(state => state.setOrderToBeShownInOrderAccept);
    const [errorMessage, setErrorMessage] = useState('');
    const [openErrorPopUp, setOpenErrorPopUp] = useState(false);
    const errorPopupDetail = useSellerOrderStore(state => state.errorPopupDetail);
    const setErrorPopupDetail = useSellerOrderStore(state => state.setErrorPopupDetail);
    const [isReminderPopup, setIsReminderPopup] = useState(true);
    const sellerSettingsData = useGlobalStore(state => state.sellerSettingsData);
    const sellerCompanyName = useGlobalStore(state => state.sellerCompanyName);
    const [openReminderYouAreAlmostTherePopup, setOpenReminderYouAreAlmostTherePopup] = useState(sellerSettingsData ? false : true);
    const [openSubmitApp, setOpenSubmitApp] = useState(showPopup && sellerSettingsData);
    const [openOrderConfirmation, setOpenOrderConfirmation] = useState(false);
    const [disableOnclick, setDisableOnclick] = useState(false);

    const saveSellerViewedOrder = useSaveSellerViewedOrder();

    
    const state = stateRef?.find(stateDetail => stateDetail.id == orderDetail?.state_id)?.code;
    const deliveryDate = moment.utc(orderDetail?.delivery_date).tz('America/Chicago').format('MMM DD, YYYY');
    const availableAfter = referenceData?.ref_general_settings.find(setting => setting.name === referenceDataKeys.sellerAvailInMinKey).value;
    const domesticMaterialText = referenceData?.ref_general_settings.find(setting => setting.name === referenceDataKeys.domesticMaterialTextKey).value;
    const createdDate = orderDetail.payment_method === purchaseOrder.paymentMethodBNPL ? orderDetail.created_date : orderDetail.ach_po_approved_date;
    const availableTime = moment.utc(createdDate).add((+availableAfter) + 1, 'minute').local().format('h:mm a');
    const materialValue = +orderDetail.seller_po_price;
    const salesTaxValue = +orderDetail.seller_sales_tax;
    const totalOrderValue = (materialValue + salesTaxValue).toFixed(2);
    const startIndex = 0;
    const lastIndex = filteredPoList.length - 1;
    const productsContainerRef = useRef();
    orderDetail.createdDate = createdDate;
    orderDetail.userType = userRole.sellerUser;
    orderDetail.deliveryDate = moment.utc(orderDetail?.delivery_date).tz('America/Chicago').format('M/D/YY');
    orderDetail.createDateFormat = moment.utc(createdDate).tz('America/Chicago').format('M/D/YY');
    orderDetail.stateCode = state;
    orderDetail.totalOrderValue = totalOrderValue;
    orderDetail.sellerCompanyName = sellerCompanyName;


    useEffect(() => {
        setLoadComponent(<ClaimOrderRightWindow />);
        return () => {
            setLoadComponent(null);
            setProps(null);
        }
    }, []);


    useEffect(() => {
        if (sellerSettingsData) {
            setOpenReminderYouAreAlmostTherePopup(false);
        } else {
            setDisableOnclick(true)
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }, [sellerSettingsData]);

    useEffect(() => {
        if (!isEditMode)
            setPoIndex(filteredPoList.findIndex(order => order.buyer_po_number === orderDetail.buyer_po_number));
    }, [filteredPoList]);


    const backNavigation = useGlobalStore(state => state.backNavigation);
    const setBackNavigation = useGlobalStore(state => state.setBackNavigation);

    useEffect(() => {
        const order = filteredPoList[poIndex] ? filteredPoList[poIndex] : orderDetail;
        order.items?.sort((a, b) => {
            return a.po_line - b.po_line;
        });
        if (!order.is_order_view) {
            saveSellerViewedOrder.mutateAsync({ data: { id: order.id } }).then(async () => {
                if (ordersCart) {
                    ordersCart[index].is_order_view = true;
                    setPOCart([...ordersCart]);
                }
            }).catch(e => {
                console.log(e)
            });
        }
        setOrderDetail(order);
    }, [poIndex, filteredPoList]);

    useEffect(() => {
        if (navigatePageTo) {
            changePoToBeAccepted(null);
            setNavigatePageTo('');
            setOpenOrderConfirmation(true)
            setLoadComponent(null)
        }
    }, [navigatePageTo]);

    useEffect(() => {
        if (errorPopupDetail) {
            setOpenSubmitApp(false);
            setErrorMessage(errorPopupDetail);
            setBackNavigation(-3)
            setOpenErrorPopUp(true);
        }
    }, [errorPopupDetail]);


    const handleScroll = () => {
        if (productsContainerRef.current.scrollTop !== 0)
            setShowScrollDownToSee(false);
    };

    const handleClickOpen = ($event) => {
        $event.stopPropagation();
        if (sellerSettingsData) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setOpenSubmitApp(true);
        } else {
            setIsReminderPopup(false);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    };

    const handleErrorClose = ($event) => {
        $event.stopPropagation();
        setOpenErrorPopUp(false);
        setErrorPopupDetail(null);
        navigate(routes.orderPage);
    };
    const handleSubmitClose = ($event) => {
        $event.stopPropagation();
        setOpenSubmitApp(false);
    };
    const previousPage = () => {
        if (poIndex > startIndex) {
            scrollToTopOfOrdersList();
            setShowScrollDownToSee(true);
            setPoIndex((state) => state - 1);

        }
    }
    const nextPage = () => {
        if (poIndex < lastIndex) {
            scrollToTopOfOrdersList();
            setShowScrollDownToSee(true);
            setPoIndex((state) => state + 1);

        }
    }

    const acceptOrder = ($event) => {
        const socket = getSocketConnection();
        console.log("Accepting Order..." + orderDetail.buyer_po_number);
        const payload = {
            buyer_po_number: orderDetail.buyer_po_number,
            super_admin_id: originalLoggedInUserData?.id ? String(originalLoggedInUserData.id) : undefined
        }
        socket.emit('acceptPo', payload);
        $event.stopPropagation();
        setOpenSubmitApp(false);
        changePoToBeAccepted(orderDetail.buyer_po_number);
    }

    const ErrorDialog = memo((props) => {
        return (
            <Dialog
                open={props.open}
                onClose={props.onClose}
                transitionDuration={200}
                hideBackdrop
                container={props.dialogRef}
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
                classes={{
                    root: styles.ErrorDialog,
                    paper: styles.dialogContent
                }}

            >
                <span className={styles.closeIcon} onClick={props.onClose}><CloseIcon /></span>
                <p className={styles.youJustMissetext} >YOU JUST MISSED IT!</p>
                <p className={styles.thisOrderMissedtest}>This order was <i>just </i> claimed <br />
                    by another Seller.</p>
                <p className={styles.missedtest}>Continue to review more orders & click “Accept Order” as soon as you know you can fulfill the order. Good luck!</p>
                <button className={styles.claimAnotherOrderbtn} onClick={($event) => props.onClose($event)}>Claim Another Order</button>
            </Dialog>
        )
    })

    function display(data) {
        const lines = data.split('\n');
        const firstLine = lines[0];
        const restLines = lines.slice(1);

        return (
            <div>
                <p className={styles.firstLineDesc}>{firstLine}</p>
                {restLines.map((line, index) => (
                    <p key={index}>{line}</p>
                ))}
            </div>
        );
    }

    const productListing = orderDetail?.items?.map((product, index) => {
        return (<tr key={index}>
            <td><span className={styles.rowIndex}>{index + 1}</span></td>
            <td >
                <span className={styles.description}>
                    {display(product.description)}
                </span>
                {(product.domestic_material_only > 0) && <div className={styles.domesticMaterial}>
                    {domesticMaterialText}
                </div>}
            </td>
            <td>
                <span className={styles.prdQty}>{formatToTwoDecimalPlaces(product.qty)}</span>
                <span className={styles.prdQtyUnit}>{product.qty_unit}</span>
            </td>
            <td>
                <span className={styles.priceUnit}>
                    {product.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(product.seller_price_per_unit) : formatToTwoDecimalPlaces(product.seller_price_per_unit)}
                </span>
                <span className={styles.prdQtyUnit}>{product.price_unit}</span>
            </td>
            <td><span className={styles.div1}>$</span><span className={styles.div2}>{formatToTwoDecimalPlaces(product.seller_line_total)}</span></td>
        </tr>);
    })

    const disablePreviousButton = startIndex === poIndex;
    const disableNextButton = lastIndex === poIndex;

    const scrollToBottomOfOrdersList = () => {
        setShowScrollDownToSee(false);
        productsContainerRef.current.scrollTop = productsContainerRef.current.scrollHeight;
    }
    const scrollToTopOfOrdersList = () => {
        productsContainerRef.current.scrollTop = 0;
    }

    const handleExportPDfClick = ($event) => {
        $event.stopPropagation();
        if (sellerSettingsData) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setDisableOnclick(false)
        } else {
            setIsReminderPopup(false);
            setDisableOnclick(true);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }

    const calculateSellerLineWeight = (data) => {
        return formatToTwoDecimalPlaces(data.total_weight)
    }
    const getCartItems = () => {
        const { items } = orderDetail;
        const formattedItems = items.map((item, index) => ({
            description: descriptionLines(item.description),
            otherDescription: getOtherDescriptionLines(item.description),
            product_tag: item.product_tag,
            domesticMaterialOnly: item.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item.qty),
            qty_unit: item.qty_unit,
            price_unit: item.price_unit,
            price: item.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(item.seller_price_per_unit) : formatToTwoDecimalPlaces(item.seller_price_per_unit),
            line_weight: calculateSellerLineWeight(item),
            extended: formatToTwoDecimalPlaces(item.seller_line_total),
            line_weight_unit: "Lb",
            line_no: index,
            po_line: index.toString(),
            total_weight: item.total_weight
        }));
        return formattedItems
    }

    
    useEffect(() => {
        setProps({
            orderDetail, availableTime, totalOrderValue, disableNextButton, disableOnclick, dialogRef, openSubmitApp,
            handleClickOpen, nextPage, setOpenReminderYouAreAlmostTherePopup, setDisableOnclick,setIsReminderPopup
        });
    }, [orderDetail, availableTime, totalOrderValue, disableOnclick, dialogRef, orderDetail.claimed_by, openSubmitApp]);

    return (
        <>
            {!openOrderConfirmation ?
                <>
                    <SearchHeader />
                    <div>
                        {/* {isEditMode &&
                        <div className={styles.disputeHeader}>
                            PO# {orderDetail.po_number}
                        </div>
                    } */}
                        <div className={clsx(styles.acceptOrderContent, 'bgBlurContent')} ref={dialogRef}>
                            <ErrorDialog
                                open={openErrorPopUp}
                                onClose={$event => handleErrorClose($event)}
                                errorMessage={errorMessage}
                                dialogRef={dialogRef.current}
                            />
                            {/* {!isEditMode && <div className={styles.acceptOrderHead} >
                            { disablePreviousButton ? <span>
                                <button className={clsx(styles.btnPreNextPo, disablePreviousButton && styles.nextprevdisabledBtn )} disabled={disablePreviousButton} onClick={previousPage}><span>Prev <br />PO</span></button>
                            </span> :
                            <>
                            <CommonTooltip
                            title={"Click here to jump back to a previous order from the Order Summary screen"}
                            tooltiplabel={<button className={clsx(styles.btnPreNextPo, disablePreviousButton && styles.nextprevdisabledBtn )} disabled={disablePreviousButton} onClick={previousPage}><span>Prev <br />PO</span></button>}
                            placement={'right-start'}
                            classes={{
                                popper: 'tooltipPopper',
                                tooltip: 'tooltipMain2 tooltipMain5',
                                arrow: 'tooltipLeftArrow1'
                        }}
                            localStorageKey="preBtnOfOrdersTooltip"
                        />
                        </>
                            }
                        
                            {orderDetail.claimed_by === purchaseOrder.pending ?
                                <CommonTooltip
                                    className={styles.orderPreviewBtnMain}
                                    title={"Orders will sit in Preview Mode for 60 minutes before being available to accept. This will give you the opportunity to review this order prior to accepting it"}
                                    tooltiplabel={
                                        <button className={styles.orderPreviewBtn}>
                                            <span className={styles.leftIcon}><WarningIcon /></span>
                                            <span className={styles.rightIcon}><WarningIcon /></span>
                                            <span>Order Preview</span>
                                            <span className={styles.acceptReview}>Available to Accept at {availableTime}</span>
                                        </button>
                                    }
                                    placement={'bottom'}
                                    classes={{
                                        popper: 'tooltipPopper',
                                        tooltip: 'tooltipMain2 tooltipMain7',
                                        arrow: 'tooltipLeftArrow1'
                                    }}
                                    localStorageKey="previeBtnOfOrdersTooltip"
                                />
                                    
                                :
                                <button className={styles.acceptOrderBtn} onClick={(event) => handleClickOpen(event)}>
                                    <SubmitApplicationDialog 
                                        open={openSubmitApp} 
                                        onClose={$event => handleSubmitClose($event)} 
                                        acceptOrder={acceptOrder}
                                        dialogRef={dialogRef.current}   
                                    />
                                    <span>ACCEPT ORDER</span>
                                </button>
                            }
                            {disableNextButton ? <span>
                                <button className={clsx(styles.btnPreNextPo, disableNextButton && styles.nextprevdisabledBtn )} onClick={nextPage} disabled={disableNextButton}><span>Next PO</span></button>
                            </span> : <>
                            <CommonTooltip
                                title={"Click here to jump to the next order from the Order Summary screen"}
                                tooltiplabel={<button className={clsx(styles.btnPreNextPo, disableNextButton && styles.nextprevdisabledBtn )} onClick={nextPage} disabled={disableNextButton}><span>Next PO</span></button>}
                                placement={'left-start'}
                                classes={{
                                    popper: 'tooltipPopper',
                                    tooltip: 'tooltipMain2 tooltipMain4',
                                    arrow: 'tooltipLeftArrow1'
                            }}
                                localStorageKey="nextBtnOfOrdersTooltip"
                            />
                            </>}
                        
                        </div>
                        } */}
                            <div className={styles.acceptOrderInformation} >
                                <div className={styles.acceptOrderInfoTop}>
                                    <div className={styles.acceptOrderInformationCol1}>
                                        <div className={styles.infoRow}>
                                            <div className={styles.infoLabel}>Total Order Value:</div>
                                            <div className={styles.infoValue}>$ {formatToTwoDecimalPlaces(totalOrderValue)}</div>
                                        </div>
                                        <div className={styles.infoRow}>
                                            <div className={styles.infoLabel}>Total Weight:</div>
                                            <div className={styles.infoValue}>{formatToTwoDecimalPlaces(orderDetail.total_weight)} LBS</div>
                                        </div>
                                        <div className={styles.infoRow}>
                                            <div className={styles.infoLabel}>Delivery Destination:</div>
                                            <div className={styles.infoValue}>
                                                {orderDetail.city}, {state} {orderDetail.zip}
                                            </div>
                                        </div>
                                    </div>
                                    <div className={clsx(styles.acceptOrderInformationCol1, styles.acceptOrderInformationCol3)}>
                                    <div className={styles.infoRow}>
                                        <div className={styles.infoLabel}>Due Date:</div>
                                        <div className={styles.infoValue}>{deliveryDate}</div>
                                    </div>
                                    <div className={styles.infoRow}>
                                        <div className={styles.infoLabel}>Freight Term:</div>
                                        <div className={styles.infoValue}>
                                            {orderDetail.freight_term} (FOB Destination)<br/>
                                            <span>Total Sale Amount includes cost of material and delivery.</span>
                                        </div>
                                    </div>
                                </div>

                                    
                                </div>

                          


                            </div>

                            <div className={styles.addPoLineTable}>
                                <table>
                                    <thead>
                                        <tr>
                                            <th><span>LN</span></th>
                                            <th><span>DESCRIPTION</span></th>
                                            <th><span>QTY</span></th>
                                            <th><span>$/UNIT</span></th>
                                            <th><span>EXT ($)</span></th>
                                        </tr>
                                    </thead>
                                    <tbody ref={productsContainerRef} onScroll={handleScroll}>
                                        {productListing}
                                    </tbody>
                                </table>
                                {/* {orderDetail?.items?.length > 2 && showScrollDownToSee && (
                                    <div className={styles.scrollDownImage}>
                                        <span>
                                            <ScrollDownImage onClick={scrollToBottomOfOrdersList} />
                                        </span>
                                    </div>
                                )} */}
                            </div>
                            {/* <div className={clsx(styles.totalAmt, 'acceptOrderExportBtn', (!channelWindow?.fetchPdf || !channelWindow?.generatePdf) && styles.exportNotAvailable)}>
                                {(channelWindow?.fetchPdf || channelWindow?.generatePdf) && <div onClick={handleExportPDfClick}><PdfMakePage sellerData={orderDetail} disableOnclick={disableOnclick} getCartItems={getCartItems} parentContainer={dialogRef} /></div>}
                                <table>
                                    <tbody>

                                        <tr>
                                            <td><span className={styles.saleTax}>Material Total</span></td>
                                            <td><span className={styles.saleTax}>$</span></td>
                                            <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(orderDetail.seller_po_price)}</span></td>
                                        </tr>
                                        <tr>
                                            <td><span className={styles.saleTax}>Sales Tax</span></td>
                                            <td><span className={styles.saleTax}>$</span></td>
                                            <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(orderDetail.seller_sales_tax)}</span></td>
                                        </tr>
                                        <tr>
                                            <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                            <td><span className={styles.totalPurchase}>$</span></td>
                                            <td><span className={styles.totalPurchase}>{formatToTwoDecimalPlaces(totalOrderValue)}</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div> */}


                            {/* <div className={styles.acceptOrderBottom}>
                            <div className={styles.returnToSearch}><button onClick={() => navigate(backNavigation)}>Back</button></div>
                            <div className={styles.textOfCondition}>After clicking “Accept Order,” the next screen will be your order confirmation.
                                Additionally, we will send you a purchase order for your records.</div>
                        </div> */}
                        </div>

                        <SubmitApplicationDialog
                            open={openSubmitApp}
                            onClose={e => handleSubmitClose(e)}
                            acceptOrder={acceptOrder}
                            dialogRef={dialogRef.current}
                        />
                        <ReminderYouAreAlmostTherePopup open={openReminderYouAreAlmostTherePopup} close={() => setOpenReminderYouAreAlmostTherePopup(false)} isReminderPopup={isReminderPopup} dialogRef={dialogRef.current} />
                    </div>
                </>
                :
                <OrderConfirmationSeller poNumber={orderDetail.buyer_po_number} />
            }
        </>
    )
}
export default AcceptOrder;