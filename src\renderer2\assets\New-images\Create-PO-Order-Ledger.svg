<svg width="322" height="520" viewBox="0 0 322 520" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_865_1459)">
<rect width="322" height="520" rx="20" fill="#191A20"/>
<g opacity="0.8" filter="url(#filter0_f_865_1459)">
<circle cx="266" cy="568" r="40" fill="#9786FF"/>
<circle cx="266" cy="568" r="40" fill="url(#paint0_linear_865_1459)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_865_1459_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.04 0.0523858 -0.03749 0.0731898 266 571.249)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="266" cy="568" r="40" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:79.999992370605469,&#34;m01&#34;:-74.979957580566406,&#34;m02&#34;:263.4899902343750,&#34;m10&#34;:104.77156829833984,&#34;m11&#34;:146.37953186035156,&#34;m12&#34;:445.67318725585938},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g filter="url(#filter1_f_865_1459)">
<circle cx="84" cy="-80" r="80" fill="#9786FF"/>
<circle cx="84" cy="-80" r="80" fill="url(#paint2_linear_865_1459)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_865_1459_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.08 0.104772 -0.07498 0.14638 84 -73.5025)"><foreignObject x="-941.285" y="-941.285" width="1882.57" height="1882.57"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="84" cy="-80" r="80" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:159.99998474121094,&#34;m01&#34;:-149.95991516113281,&#34;m02&#34;:78.979965209960938,&#34;m10&#34;:209.54313659667969,&#34;m11&#34;:292.75906372070312,&#34;m12&#34;:-324.65365600585938},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="321" height="519" rx="19.5" stroke="url(#paint4_radial_865_1459)" stroke-opacity="0.4"/>
<defs>
<filter id="filter0_f_865_1459" x="166" y="468" width="200" height="200" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_865_1459"/>
</filter>
<clipPath id="paint1_angular_865_1459_clip_path"><circle cx="266" cy="568" r="40"/></clipPath><filter id="filter1_f_865_1459" x="-96" y="-260" width="360" height="360" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_865_1459"/>
</filter>
<clipPath id="paint3_angular_865_1459_clip_path"><circle cx="84" cy="-80" r="80"/></clipPath><linearGradient id="paint0_linear_865_1459" x1="266" y1="552.162" x2="320.3" y2="581.809" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_865_1459" x1="84" y1="-111.675" x2="192.6" y2="-52.3818" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_865_1459" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(99.5517 -8.76744) rotate(104.922) scale(129.218 81.103)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_865_1459">
<rect width="322" height="520" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
