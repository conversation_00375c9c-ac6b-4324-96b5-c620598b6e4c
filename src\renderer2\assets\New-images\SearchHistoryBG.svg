<svg width="320" height="600" viewBox="0 0 320 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#1E2735" />
      <stop offset="100%" stop-color="#121A28" />
    </linearGradient>
    <filter id="shadowFilter" x="-10%" y="-10%" width="120%" height="120%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="5" />
      <feOffset dx="0" dy="4" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.5" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main Background -->
  <rect width="320" height="600" rx="15" fill="url(#bgGradient)" filter="url(#shadowFilter)" />
  
  <!-- Top highlight -->
  <rect width="320" height="1" fill="rgba(255, 255, 255, 0.1)" />
  
  <!-- Left highlight -->
  <rect width="1" height="600" fill="rgba(255, 255, 255, 0.05)" />
  
  <!-- Subtle pattern overlay -->
  <rect width="320" height="600" rx="15" fill="url(#noisePattern)" opacity="0.025" />
  
  <!-- Gradient overlay at the top for header area -->
  <rect width="320" height="60" fill="rgba(0, 0, 0, 0.15)" />
  
  <pattern id="noisePattern" width="100" height="100" patternUnits="userSpaceOnUse">
    <path d="M0 0h100v100H0z" fill="none"/>
    <rect width="1" height="1" x="25" y="25" fill="rgba(255, 255, 255, 0.05)"/>
    <rect width="1" height="1" x="75" y="50" fill="rgba(255, 255, 255, 0.05)"/>
    <rect width="1" height="1" x="50" y="75" fill="rgba(255, 255, 255, 0.05)"/>
    <rect width="1" height="1" x="10" y="90" fill="rgba(255, 255, 255, 0.05)"/>
    <rect width="1" height="1" x="90" y="10" fill="rgba(255, 255, 255, 0.05)"/>
  </pattern>
</svg> 