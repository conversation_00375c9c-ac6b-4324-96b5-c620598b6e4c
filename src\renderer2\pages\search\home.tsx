import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { useSellerOrderStore, useGlobalStore, selectProduct, dataOpticsApi1, dataOpticsApi2, userRole, referenceProductItem, getFormattedUnit, useBuyerSettingStore, priceUnits } from '@bryzos/giss-ui-library';
import { ProductPricingModel, SearchAnalyticDataModel, HttpRequestPayload } from '../../types/Search';
import styles from './home.module.scss'
import clsx from 'clsx';
import SearchHeader from '../SearchHeader';
import { useSearchStore } from '../../store/SearchStore';
import SearchLeftSection from './searchLeftSection/SearchLeftSection';
import SearchResultPanel from './mainContent/SearchResultPanel';
import SelectedProductList from './mainContent/SelectedProductList';
import SearchRightSection from './searchRightSection/SearchRightSection';
import MainContent from './mainContent/MainContent';
import { getPriceExample } from 'src/renderer2/utility/priceIntegratorExample';
import ProductSearch from './headerSection/productSearch';
import { neutralPricingPrefix } from 'src/renderer2/utility/moveToLibraryCode';

const Home = () => {
    const { enableShareWidget, setEnableShareWidget, setShowLoader, setSearchSessionId, searchSessionId, userData, discountData, productMapping, referenceDataUpdated }: any = useGlobalStore();

    const { shortListedSearchProductsData, setShortListedSearchProductsData, searchByProductResult, selectedProductsData, setSelectedProductsData, selectedPriceUnit, selectedDomesticOption, setFilterShortListedSearchProductsData, setFilterSearchByProductResult, sessionId, setSessionId, searchZipCode, orderSizeSliderValue, resetSearchStore } = useSearchStore();

    const [showWidgetPanel, setShowWidgetPanel] = useState<boolean>(false);
    const { resetFiltersInPurchaseOrders }: any = useSellerOrderStore();
    const analyticRef = useRef<string>();
    const [isShareWidget, setIsShareWidget] = useState<boolean>(false);
    analyticRef.current = sessionId;


    useEffect(() => {
        resetFiltersInPurchaseOrders();
        setShowLoader(false);
        if (searchSessionId) {
            setSessionId(searchSessionId)
        } else {
            const sessionId = uuidv4();
            setSessionId(sessionId);
        }

        return () => {
            setEnableShareWidget(false);
            const dataOpticsPayload = {
                "data": {
                    "session_id": analyticRef.current,
                    "move_to_screen": location.pathname.replace('/', "")
                }
            }
            dataOpticsApi2(dataOpticsPayload)
            resetSearchStore();
        }
    }, []);

    useEffect(() => {
        if (enableShareWidget) {
            handleOpenWidget();
        }
        else {
            handleCloseWidget();
        }
    }, [enableShareWidget])

    useEffect(() => {
        if (sessionId) {
            setSearchSessionId(sessionId);
        }
    }, [sessionId])

    useEffect(() => {
        const processProducts = async () => {
            if (productMapping && shortListedSearchProductsData?.length) {
                fetchPrice(searchZipCode, orderSizeSliderValue);
            }
            else {
                setShortListedSearchProductsData([]);
            }
        };

        processProducts();
    }, [discountData, productMapping, referenceDataUpdated]);


    useEffect(() => {
        filterSearchByProductList();
        filterSelectedSearchProductList();
    }, [selectedDomesticOption, shortListedSearchProductsData]);

    useEffect(() => {
        filterSearchByProductList()
    }, [searchByProductResult]);

    useEffect(() => {
        if (shortListedSearchProductsData.length > 0) {
            fetchPrice(searchZipCode, orderSizeSliderValue);
        }
    }, [searchZipCode, orderSizeSliderValue])
    
    const fetchPrice = async (zipCode: string, orderSize: number) => {
        const filteredProductData = shortListedSearchProductsData.filter((product) => !product.is_safe_product_code);
        const productIdList = filteredProductData.map((product) => product.id);
        let newPrices = {};
        if(productIdList.length > 0){
            newPrices = await getPriceExample(productIdList, zipCode, Math.floor(orderSize));
        }
        const formattedPrices = shortListedSearchProductsData.map((product: ProductPricingModel) => {
            const selectedProduct = {...product};
            Object.keys(priceUnits).forEach((key: any) => {
                (newPrices?.[selectedProduct.id]?.[key] || selectedProduct?.[neutralPricingPrefix + key]) &&
                    (selectedProduct[`${key}_price`] = (selectedProduct.is_safe_product_code) ? selectedProduct?.[neutralPricingPrefix + key] : (newPrices[selectedProduct.id]?.[key] || 0));
            })
            return selectedProduct;
        })
        // const formattedPrices = shortListedSearchProductsData.map((selectedProduct: ProductPricingModel) => {
        //     let cwt_price = selectedProduct.is_safe_product_code ? selectedProduct?.[neutralPricingPrefix+ priceUnits.cwt] : 0;
        //     let lb_price = selectedProduct.is_safe_product_code ? selectedProduct?.[neutralPricingPrefix+ priceUnits.lb] : 0;
        //     let ft_price = selectedProduct.is_safe_product_code ? selectedProduct?.[neutralPricingPrefix+ priceUnits.ft] : 0;
        //     let pc_price = selectedProduct.is_safe_product_code ? selectedProduct?.[neutralPricingPrefix+ priceUnits.ea] : 0;
        //     if(newPrices?.[selectedProduct.id]){
        //         const prices = newPrices[selectedProduct.id];
        //         cwt_price = prices.cwt;
        //         lb_price = prices.lb;
        //         ft_price = prices.ft;
        //         pc_price = prices.pc;
        //     }
        //     return {
        //         ...selectedProduct,
        //         cwt_price,
        //         lb_price,
        //         ft_price,
        //         pc_price
        //     }
        // });
        setShortListedSearchProductsData([...formattedPrices]);
    }

    const filterSearchByProductList = () => {
        if (searchByProductResult.length !== 0) {
            let filteredSearchByProductResult = [...searchByProductResult];
            if (selectedDomesticOption) {
                filteredSearchByProductResult = filteredSearchByProductResult.filter(product => product.domestic_material_only);
            }
            setFilterSearchByProductResult(filteredSearchByProductResult);
        } else {
            setFilterSearchByProductResult([]);
        }
    }

    const filterSelectedSearchProductList = () => {
        if (shortListedSearchProductsData.length !== 0) {
            let filteredSelectedProductSearchData = [...shortListedSearchProductsData];
            if (selectedDomesticOption) {
                filteredSelectedProductSearchData = shortListedSearchProductsData.filter((selectedProduct) => selectedProduct.domestic_material_only);
            }
            setFilterShortListedSearchProductsData(filteredSelectedProductSearchData);
        } else {
            setFilterShortListedSearchProductsData([]);
        }
    }

    const handleOpenWidget = () => {
        setShowWidgetPanel(true);
        setIsShareWidget(true);
    };
    const handleCloseWidget = () => {
        setShowWidgetPanel(false);
        setIsShareWidget(false);
        setEnableShareWidget(false);
    };

    const onShareProductPricing = async (emailTo: string, emailContent: string): Promise<void> => {
        const _selectedProduct: ProductPricingModel[] = selectedProductsData.length === 0 ? shortListedSearchProductsData : selectedProductsData;
        const productList: any[] = [];
        const dataOpticsData: SearchAnalyticDataModel[] = [];
        const { buyerSetting } = useBuyerSettingStore.getState();
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const _zipcode = searchZipCode?.length === 5 ? searchZipCode :  defaultZipCode;
        _selectedProduct.forEach((product: ProductPricingModel) => {
            productList.push({
                "product_id": product.id,
                "product_description": product.UI_Description,
                "price_ft": product.ft_price.trim().replace("$", ""),
                "price_lb": product.lb_price.trim().replace("$", ""),
                "price_cwt": product.cwt_price.trim().replace("$", ""),
                "price_share_type": selectedPriceUnit !== 'cwt,ft' ? getFormattedUnit(selectedPriceUnit)?.toLowerCase() : product.product_type_pipe ? "ft" : "cwt",
            });
            dataOpticsData.push({
                "session_id": sessionId,
                "line_session_id": product.line_session_id,
                "product_id": product.id,
                "description": product.UI_Description,
                "price_shared": true,
                "search_price_unit": selectedPriceUnit !== 'cwt,ft' ? getFormattedUnit(selectedPriceUnit)?.toLowerCase() : product.product_type_pipe ? "ft" : "cwt",
                "zip_code": _zipcode.trim(),
                "order_size": String(orderSizeSliderValue),
                "price" : {
                    "price_ft": product?.ft_price,
                    "price_lb": product?.lb_price,
                    "price_cwt": product?.cwt_price,
                    "price_pc": product?.pc_price,
                },
            })
        });

        const dataOpticsPayload: HttpRequestPayload<SearchAnalyticDataModel[]> = {
            "data": dataOpticsData
        }
        dataOpticsApi1(dataOpticsPayload)
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent.length === 0 ? null : emailContent,
                "products": productList,
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareProductPrice', payload)
            setSelectedProductsData([]);
            return res.data.data
        } catch (err) {
            throw new Error("Share Product Pricing Api Failure");
        }
    }

    const onShareApp = async (emailTo: string, emailContent: string): Promise<void> => {
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent.length === 0 ? null : emailContent
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareWidgetRequest', payload)
            return res.data.data;
        } catch (err) {
            throw new Error("Share App Api Failure");
        }
    }

    const shareHandler = isShareWidget ? onShareApp : onShareProductPricing;

    const handleSharePrice = () => {
        setShowWidgetPanel(true);
    }

    return (
        <div className={styles.mainContent}>
            {/* <SearchHeader /> */}
            <ProductSearch />
            <div className={styles.innerContent}>
                {/* <SearchLeftSection /> */}
                <MainContent />
                {/* <SearchRightSection /> */}
            </div>
        </div>
    );
};

export default Home;

