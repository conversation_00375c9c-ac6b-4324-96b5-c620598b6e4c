import { routes } from 'src/renderer2/common';
import styles from './leftPanel.module.scss';
import ListTab from './ListTab/ListTab';
import RouteTab from './RouteTab/RouteTab';
import { useLocation } from 'react-router-dom';
import PdfNavigator from 'src/renderer2/pages/buyer/BomPdfExtractor/components/PdfNavigator';

const LeftPanel = () => {
    const location = useLocation();
    if (location.pathname === routes.loginPage ||
        location.pathname === routes.forgotPassword
        || location.pathname === routes.onboardingWelcome ||
        location.pathname === routes.changePassword
        || location.pathname === routes.onboardingDetails)
        return <></>

    return (
        <div className={styles.leftPanel}>
            <div className={styles.routeTab}>
                <RouteTab />
            </div>
          {location.pathname === routes.bomExtractor ?  <div className={styles.pdfNavigator}>
                <PdfNavigator/>
            </div> : <div className={styles.listTab}>
                <ListTab />
            </div>

          } 
           
        </div>
    )
}

export default LeftPanel;