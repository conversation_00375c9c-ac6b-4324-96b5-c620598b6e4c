import React, { useCallback, useState } from 'react';
import PdfViewer from './components/PdfViewer';
import SearchHeader from '../../SearchHeader';

interface BomPdfExtractorProps {
  /** Optional title to display */
  title?: string;
}

interface AppState {
  [key: string]: any;
}

const BomPdfExtractor: React.FC<BomPdfExtractorProps> = ({ title = 'BomPdfExtractor' }) => {
    const [appState, setAppState] = useState<AppState>({});
    const updateAppState = useCallback((newState: AppState) => {
        setAppState((prevState) => {
          // Check if the new state is actually different from the current state
          // to prevent unnecessary re-renders
          const hasChanges = Object.keys(newState).some((key) => {
            // For objects, we do a simple reference check
            // For primitives, we do a value check
            return prevState[key] !== newState[key];
          });
    
          if (!hasChanges) {
            return prevState; // No changes, return the previous state
          }
    
          return {
            ...prevState,
            ...newState,
          };
        });
      }, []);
  return (
    <div style={{color:'white', height:"100%"}}>
      {/* <SearchHeader title="BomPdfExtractor" /> */}
      <PdfViewer  onStateChange={updateAppState} />
    </div>
  );
};

export default BomPdfExtractor;
