.container {
  width: 100vw;
  height: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 600px;
}

.appDrag {
  -webkit-app-region: drag;
  width: 88%;
  height: 55px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  border-radius: 12px 0px 0px 0px;
}

.content {
  background: url(../../assets/New-images/AppBG.svg) no-repeat;
  background-size: cover;
  border: 1px solid #2a2a2a;
  border-radius: 20px;
  padding: 48px 32px;
  text-align: center;
  width: 100%;
  min-width: 400px;
  max-width: 800px;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.windowControls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.minimizeBtn,
.closeBtn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  color: #888888;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #cccccc;
  }

  svg {
    width: 12px;
    height: 12px;
  }
}

.closeBtn {
  &:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
  }
}

.minimizeBtn {
  &:hover {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff44;
  }
}

.icon {
  margin-bottom: 24px;
  color: #00ff00;

  svg {
    width: 120px;
    height: auto;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.loadingIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #333333;
  border-top: 2px solid #00ff00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 14px;
  color: #888888;
  font-weight: 500;
  font-family: Noto Sans;
}

.appUpdateNotifiMain {
  display: flex;
  flex-direction: column;
  align-items: center;


  .title {
    font-size: 28px;
    font-weight: 600;
    color: #70ff00;
    margin: 0 0 20px 0;
    line-height: 1.2;
    font-family: Noto Sans;
  }

  .message {
    font-size: 18px;
    color: #fff;
    line-height: 1.6;
    margin: 0 0 40px 0;
    font-family: Noto Sans;
  }

  .updateLink {
    background: none;
    border: none;
    color: #00ff00;
    font-size: 18px;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
        font-family: Noto Sans;


    &:hover {
      color: #00cc00;
      background: transparent;
      text-decoration: none;
    }

    &:focus {
      outline: 2px solid #00ff00;
      outline-offset: 2px;
      border-radius: 4px;
    }
  }

}