// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { useGlobalStore, userRole } from "@bryzos/giss-ui-library";

const useGetUserSubscription = () => {
  const { setShowLoader, setUserSubscription, subscriptionStatus, userData } = useGlobalStore();

  return useQuery(
    [reactQueryKeys.getUserSubscription, subscriptionStatus, userData?.data?.email],
    async () => {
      try {
        const response = axios.get(import.meta.env.VITE_API_SERVICE + '/subscription');
        const responseData = await response;
        
        if (responseData.data && responseData.data.data) {
            const userSubscription = responseData.data.data;
            setUserSubscription(userSubscription);
            return userSubscription;
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      } 
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      staleTime:0,
      cacheTime:0,
      enabled: (!!userData?.data && userData?.data?.type === userRole.buyerUser)
    }
  );
};

export default useGetUserSubscription;
