import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

type GameSpeed = {
  ballSpeed: number;
  basketSpeed: number;
  increamentFactor: number;
};

const useGetGameSpeed = () => {
  return useQuery<GameSpeed | null, Error>(
    [reactQueryKeys.getGameSpeed],
    async () => {
      const response = await axios.get(
        `${import.meta.env.VITE_API_SERVICE}/user/game-initial-settings`
      );

      const data = response.data?.data;

      if (data) {
        if (typeof data === "object" && "error_message" in data) {
          throw new Error(data.error_message);
        }
        return data;
      }

      return null;
    },
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,
    }
  );
};

export default useGetGameSpeed;
