<svg width="480" height="50" viewBox="0 0 480 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_189_8874)">
<rect width="480" height="50" rx="12" fill="white" fill-opacity="0.1"/>
<g opacity="0.44" filter="url(#filter0_f_189_8874)">
<ellipse cx="420.357" cy="75" rx="59.6429" ry="25" fill="#9786FF"/>
<ellipse cx="420.357" cy="75" rx="59.6429" ry="25" fill="url(#paint0_linear_189_8874)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_189_8874_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0596428 0.0327411 -0.0559002 0.0457436 420.357 77.0305)"><foreignObject x="-952.096" y="-952.096" width="1904.19" height="1904.19"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="420.357" cy="75" rx="59.6429" ry="25" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:119.28569793701172,&#34;m01&#34;:-111.80047607421875,&#34;m02&#34;:416.61459350585938,&#34;m10&#34;:65.482231140136719,&#34;m11&#34;:91.487205505371094,&#34;m12&#34;:-1.4542640447616577},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.87" filter="url(#filter1_f_189_8874)">
<circle cx="35" cy="-35" r="35" fill="#9786FF"/>
<circle cx="35" cy="-35" r="35" fill="url(#paint2_linear_189_8874)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_189_8874_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.035 0.0458376 -0.0328037 0.064041 35 -32.1574)"><foreignObject x="-955.63" y="-955.63" width="1911.26" height="1911.26"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="35" cy="-35" r="35" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:69.999992370605469,&#34;m01&#34;:-65.607460021972656,&#34;m02&#34;:32.803737640380859,&#34;m10&#34;:91.675125122070312,&#34;m11&#34;:128.08209228515625,&#34;m12&#34;:-142.03596496582031},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<defs>
<filter id="filter0_f_189_8874" x="301.18" y="-9.53443" width="238.355" height="169.069" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="29.7672" result="effect1_foregroundBlur_189_8874"/>
</filter>
<clipPath id="paint1_angular_189_8874_clip_path"><ellipse cx="420.357" cy="75" rx="59.6429" ry="25"/></clipPath><filter id="filter1_f_189_8874" x="-99.2241" y="-169.224" width="268.448" height="268.448" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="49.612" result="effect1_foregroundBlur_189_8874"/>
</filter>
<clipPath id="paint3_angular_189_8874_clip_path"><circle cx="35" cy="-35" r="35"/></clipPath><linearGradient id="paint0_linear_189_8874" x1="420.357" y1="65.1015" x2="459.332" y2="115.868" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_189_8874" x1="35" y1="-48.8579" x2="82.5123" y2="-22.917" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_189_8874">
<rect width="480" height="50" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
