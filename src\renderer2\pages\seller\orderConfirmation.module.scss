// .buyerorderConfirmationContent {
//     padding: 12px 16px;
//     border-radius: 0px 0px 10px 10px;
//     margin: 0px auto;
//     max-width: 800px;
//     width: 100%;
//     text-align: center;

//     .orderConfirmation {
//         font-family: Noto Sans;
//         font-size: 28px;
//         font-weight: bold;
//         line-height: 1.6;
//         text-align: center;
//         margin-bottom: 12px;
//         font-weight: 600;
//         color: #70ff00;
//     }

//     .emailHeder {
//         .orderEmail {
//             font-family: Noto Sans;
//             font-size: 16px;
//             font-weight: 300;
//             font-stretch: normal;
//             font-style: normal;
//             line-height: 1.6;
//             letter-spacing: normal;
//             text-align: center;
//             color: #fff;
//         }
//         .emailIdInvoice {
//             font-weight: 600;
//         } 
//     }

//     .poBoxOne {
//         padding: 20px 0px 8px 0px;

//         .poNumberCreator {
//             font-family: Noto Sans;
//             font-size: 18px;
//             font-weight: bold;
//             line-height: 1.6;
//             letter-spacing: 1.8px;
//             color: #fff;
//             width: 100%;
//             display: flex;
//             align-items: center;
//             justify-content: center;
        
//         }
//         .poNumber {
//             font-size: 14px;
//             font-weight: 300;
//             line-height: 1.6;
//             padding-left: 8px;
//         }
//     }

//     .poNumber {
//         font-family: Noto Sans;
//         font-size: 16px;
//         font-weight: 300;
//         line-height: 1.6;
//         text-align: center;
//         color: #fff;
//     }

//     .uploadYourPo1 {
//         display: flex;
//         justify-content: center;
//         align-items: center;
//         font-family: Noto Sans;
//         color: #fff;
//         margin: 12px auto;
//         position: relative;

//         .uploadYourPoDiv {
//             width: 360px;
//             height: 64px;
//             padding: 8px 0;
//             border-radius: 8px;
//             background-color: rgba(0, 0, 0, 0.25);
//             display: flex;
//             align-items: center;
//             justify-content: center;
//             flex-direction: column;
//             &:hover {
//                 border: 1px solid #42ff00;;
//             }

//         }

//         .disabled {
//             opacity: 0.5;
//             cursor: not-allowed;
//         }

//         .svgDiv {
//             position: absolute;
//             right: 65px;
//             transition: all 0.1s;

//             .questionIcon2 {
//                 display: none;
//                 width: 24px;
//                 height: 24px;
//             }
//             &:hover {
//                 .questionIcon1 {
//                     display: none;
//                 }

//                 .questionIcon2 {
//                     display: inline-block;
//                 }
//             }
//         }

//         input {
//             display: none;
//         }
//     }

//     .emailIdInter {
//         font-family: Noto Sans;
//         font-size: 14px;
//         line-height: 1.4;
//         text-align: center;
//         color: #fff;
//         font-weight: 300;
//     }

//     .uploadYourPo {
//         width: 360px;
//         height: 64px;
//         display: flex;
//         flex-direction: column;
//         justify-content: center;
//         align-items: center;
//         font-family: Noto Sans;
//         padding: 8px 0;
//         border-radius: 8px;
//         background-color: rgba(0, 0, 0, 0.25);
//         color: #fff;
//         margin: 12px auto;

//         &:hover {
//             border: 1px solid #42ff00;;
//         }
//     }

//     .disabled {
//         opacity: 0.5;
//         cursor: not-allowed;
//     }

//     .returnInstantPricing {
//         font-family: Noto Sans;
//         font-size: 16px;
//         line-height: 1.6;
//         color: #fff;
//         margin: 24px auto;
//         display: flex;
//         justify-content: center;
//         text-align: center;
//         align-items: center;
//         gap: 4px;
//         cursor: pointer;

//         &:hover {
//             color: #70ff00;
//         }
//     }

//     .pointer {
//         cursor: pointer;
//     }
// }

// .ConfirmationDialog {
//     .dialogContent {
//         max-width: 300px;
//         width: 100%;
//         display: flex;
//         flex-direction: column;
//         justify-content: flex-start;
//         align-items: center;
//         padding: 30px 34px 30px 34px;
//         object-fit: contain;
//         border-radius: 10px;
//         -webkit-backdrop-filter: blur(24px);
//         backdrop-filter: blur(24px);
//         box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
//         background-color: rgba(0, 0, 0, 0.72);
//         font-family: Noto Sans;
//         font-size: 14px;
//         font-weight: normal;
//         line-height: 1.6;
//         text-align: center;
//         color: #fff;

//         p {
//             margin-bottom: 20px;
//         }


//         .submitBtn {
//             transition: all 0.1s;
//             font-family: Noto Sans;
//             font-size: 16px;
//             line-height: 1.6;
//             text-align: center;
//             color: #70ff00;
//             background-color: transparent;
//             border: 0;
//             opacity: 0.7;

//             &:disabled {
//                 cursor: not-allowed;
//                 color: #fff !important;
//                 opacity: 0.5 !important;
//             }

//             &:hover {
//                 color: #70ff00;
//                 opacity: unset;
//             }
//         }

//         .cancelBtn {
//             opacity: 0.7;
//             font-family: Noto Sans;
//             font-size: 16px;
//             line-height: 1.6;
//             text-align: center;
//             color: #fff;
//             transition: all 0.1s;
//             background-color: transparent;
//             border: none;

//             &:hover {
//                 opacity: unset;
//             }
//         }

//         p {
//             padding: 20px;
//         }
//     }

//     .actionsTab {
//         display: flex;
//         width: 100%;

//         button {
//             width: 50%;
//         }
//     }
// }

// .setRatingBox {
//     width: 360px;
//     height: 64px;
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;
//     font-family: Noto Sans;
//     padding: 8px 0;
//     border-radius: 8px;
//     background-color: rgba(0, 0, 0, 0.25);
//     color: #fff;
//     margin: 12px auto;
//     &:hover {
//         border: 1px solid #42ff00;;
//     }
// }




// New styles for seller order confirmation
.orderConfirmationContainer {
    padding: 32px 99px 68px;
    background: url(../../assets/New-images/AppBG.svg) no-repeat;
    background-position: bottom;
    width: 100%;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 1px;
        left: 0;
        width: 50%;
        height: 1px;
        background: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0)); 
        z-index: 11;
      }
    .orderConfirmationContent {
        display: flex;
        flex-direction: column;
        text-align: center;
        color: #fff;
        border-radius: 16px;
        background: url(../../assets/New-images/MainBg.svg) no-repeat;
        background-size: contain;
      
        .orderConfirmationPoDetails{
            width: 100%;
            height: 111px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .orderConfirmdText {
                font-family: Syncopate;
                font-size: 26px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.35;
                letter-spacing: 1.04px;
                text-align: center;
                color: #fff;
            }
            .poNumberText {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: 8px;
                flex-direction: row;
                text-align: left;
                color: #fff;
                line-height: 1;
                font-stretch: normal;
                font-style: normal;
                .poText {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    letter-spacing: normal;
                }
                .jobText {
                    font-family: Inter;
                    font-size: 16px;
                    font-weight: 300;
                    letter-spacing: normal;
                }
                .poNumber{
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 200;
                    letter-spacing: 1.26px;
                    
                }
            }
        }
        .purchaseRating{
            width: 100%;
            height: 94px;
            box-shadow: inset 8px 9px 4px -3px #000;
            // background-image: linear-gradient(149deg, #0f0f14 -7%, #393e47 109%);
            background: url(../../assets/New-images/RatingBG.svg) no-repeat;
           
            flex-direction: row;
            display: flex;
            align-items: center;
            justify-content: end;
            padding: 24px 40px;
            gap: 56px;
            p{
                font-family: Syncopate;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.18px;
                text-align: left;
                color: #c3c4ca;
                text-transform: uppercase;
            }
            .GiveUsthumbsUp{
                font-family: Inter;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: italic;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: center;
                color: #c3c4ca;
                text-transform:none;
            }
            .purchaseRatingBtn{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }
        }
        .purchaseEmailDiv{
            display: flex;
            gap: 32px;
            align-items: center;
            justify-content: center;
            height: 100px;
            // border: solid 1px transparent;
            // background: linear-gradient(#19191e, #19191e) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 40%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;
            // border-radius: 0px 0px 16px 16px;
            .purchaseEmailTextDiv {
                display: flex;
                flex-direction: column;
                .purchaseEmailText {
                    font-family: Syncopate;
                    font-size: 16px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.8);
                    text-transform: uppercase;
                }
                .emailIdInvoice {
                    font-family: Inter;
                    font-size: 16px;
                    font-weight: 200;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: 1.28px;
                    text-align: left;
                    color: #fff;
                }
            }
        }
    }
    .orderConfirmationDefaultText {
        padding: 24px 7px 39px;
        font-family: Inter;
        font-size: 20px;
        font-weight: 200;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.65;
        letter-spacing: -0.2px;
        text-align: center;
        color: #fff;
    }
    .orderConfirmationButtons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        .orderConfirmationButtonBg {
            border-radius: 14px;
            background-color: rgba(255, 255, 255, 0.04);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            &:hover {
                .orderConfirmationButtonText {
                    color: #fff;
                    button {
                        color: #fff;
                    }
                }
                .emailIdInter {
                    color: #fff;
                }
            }
            .orderConfirmationButtonText {
                font-family: Syncopate;
                font-size: 16px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.64px;
                text-align: center;
                color: rgba(255, 255, 255, 0.4);
                text-transform: uppercase;
                button {
                    font-family: Syncopate;
                    font-size: 16px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.64px;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.4);
                    text-transform: uppercase;
                }
            }
            .emailIdInter {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: center;
                color: rgba(255, 255, 255, 0.4);
            }
            .uploadYourPoDiv {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }
            label{
                width: 100%;
                cursor: pointer;
               
            }
    
            .disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
    
            input {
                display: none;
            }    
        }
    }
    .pointer {
        cursor: pointer;
    }
    .createAnotherPurchase{
        border: 0px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        border-radius: 10px;
        background: url(../../assets/New-images/CreateDefault-Seller.svg) no-repeat ; 
        background-size: cover;
        width: 100%;
        background-position: center;    
        transition: background-image 0.2s ease-in-out;
        &:hover {
            background: url(../../assets/New-images/CreateDefaultHover-Seller.svg) no-repeat ; 
            background-size: cover;
            width: 100%;
            background-position: center;  
        }
    }
}
.thumbsImages {
    .img1 {
        display: block;
    }
    .img2 {
        display: none;
    }
    &:hover {
        .img1 {
            display: none;
        }
        .img2 {
            display: block;
        }
    }
}
.thumbsImagesActive {
    .img1 {
        display: none;
    }
    .img2 {
        display: block;
    }
}

.ConfirmationDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            transition: all 0.1s;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
            background-color: transparent;
            border: 0;
            opacity: 0.7;

            &:disabled {
                cursor: not-allowed;
                color: #fff !important;
                opacity: 0.5 !important;
            }

            &:hover {
                color: #70ff00;
                opacity: unset;
            }
        }

        .cancelBtn {
            opacity: 0.7;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            transition: all 0.1s;
            background-color: transparent;
            border: none;

            &:hover {
                opacity: unset;
            }
        }

        p {
            padding: 20px;
        }
    }

    .actionsTab {
        display: flex;
        width: 100%;

        button {
            width: 50%;
        }
    }
}
