<svg width="294" height="164" viewBox="0 0 294 164" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="1" width="292" height="162" rx="13" fill="#23242A"/>
<g filter="url(#filter0_i_984_1438)">
<path d="M1 14C1 6.8203 6.8203 1 14 1H280C287.18 1 293 6.8203 293 14V150C293 157.18 287.18 163 280 163H14C6.8203 163 1 157.18 1 150V14Z" fill="#23242A" fill-opacity="0.2"/>
</g>
<path d="M280 0.5H14C6.54416 0.5 0.5 6.54416 0.5 14V150C0.5 157.456 6.54416 163.5 14 163.5H280C287.456 163.5 293.5 157.456 293.5 150V14C293.5 6.54416 287.456 0.5 280 0.5Z" stroke="url(#paint0_linear_984_1438)" stroke-opacity="0.33"/>
<defs>
<filter id="filter0_i_984_1438" x="0" y="0" width="298" height="168" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_984_1438"/>
</filter>
<linearGradient id="paint0_linear_984_1438" x1="229.597" y1="196.652" x2="146.305" y2="-40.1114" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B20" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
